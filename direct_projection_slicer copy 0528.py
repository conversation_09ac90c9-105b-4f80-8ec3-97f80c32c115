import numpy as np
import trimesh
import os
import math # 为math.pi添加
from scipy.spatial import cKDTree
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from shapely.geometry import Polygon, LineString, MultiLineString, Point # 从shapely.geometry导入Point
import time # 导入time模块
import shapely
import csv # 添加csv模块导入

class DirectProjectionSlicer:
    def __init__(self, mesh_path, target_surface_distance=0.4, slice_direction='x', inward_normals=True, min_points_req=2):
        init_start_time = time.time()
        """
        初始化直接投影切片器

        参数:
        mesh_path: 网格文件路径（STL格式）
        target_surface_distance: 目标表面距离，主要用于二维填充参数的设定参考
        slice_direction: 切片方向，'x'或'y' (主要影响二维填充的扫描轴，以及新直接偏置策略的偏置轴)
        inward_normals: 是否将法向量方向设为朝向面内（True）或面外（False）
        min_points_req: 路径段所需的最小点数
        """
        self.mesh = trimesh.load_mesh(mesh_path)
        self.mesh_path = mesh_path
        self.d_surf = target_surface_distance
        self.min_points_req = min_points_req
        self.inward_normals = inward_normals

        # 设置切片方向 (主要用于二维填充的扫描轴确定，或直接偏置策略的偏置/扫描轴)
        # axis_index: 0 表示 X 是主扫描/步进方向 (2D填充)，或偏置方向 (direct_offset)
        # axis_index: 1 表示 Y 是主扫描/步进方向 (2D填充)，或偏置方向 (direct_offset)
        if slice_direction.lower() == 'x':
            self.axis_index = 0 # X是主扫描轴(2D)或偏置轴(direct_offset), Y是步进轴(2D)或扫描轴(direct_offset)
        elif slice_direction.lower() == 'y':
            self.axis_index = 1 # Y是主扫描轴(2D)或偏置轴(direct_offset), X是步进轴(2D)或扫描轴(direct_offset)
        else:
            raise ValueError("切片方向必须是 'x' 或 'y'")

        # 确保网格有法线
        if not hasattr(self.mesh, 'face_normals') or self.mesh.face_normals is None:
            self.mesh.generate_face_normals() # trimesh方法，用于生成面法线
        if not hasattr(self.mesh, 'vertex_normals') or self.mesh.vertex_normals is None:
            self.mesh.generate_normals() # trimesh方法，用于生成顶点法线
        
        # KD树用于快速查询顶点法线
        self.vertex_points = self.mesh.vertices
        self.vertex_normals = self.mesh.vertex_normals
        self.vertex_tree = cKDTree(self.vertex_points)

        # 获取网格边界用于射线投影的范围
        self.mesh_bounds = self.mesh.bounds
        
        # 添加法线缓存以提高性能
        self._normal_cache = {}
        self._cache_tolerance = 1e-6  # 缓存容差，距离小于此值的点共享法线
        
        # 添加边界距离缓存
        self._boundary_distance_cache = {}
        self._boundary_linestrings = []  # 预存储的边界LineString列表
        
        print(f"网格加载完成。边界: {self.mesh_bounds}")
        print(f"--- Slicer 初始化完成, 耗时: {time.time() - init_start_time:.4f} 秒 ---")

    def get_surface_normal_at_point(self, point_3d):
        """获取三维空间中给定点在原始网格曲面上的法线向量（通过最近顶点插值）。
        优化版本：添加缓存机制提高性能。"""
        
        # 检查缓存
        point_key = tuple(np.round(point_3d / self._cache_tolerance).astype(int))
        if point_key in self._normal_cache:
            return self._normal_cache[point_key]
            
        # 查询最近的顶点索引
        _, idx = self.vertex_tree.query(point_3d, k=1)
        # 获取该顶点的法线
        normal = self.mesh.vertex_normals[idx]
        
        if self.inward_normals:
            normal = -normal # 如果需要面内法线，则反转法线方向
            
        normalized_normal = normal / np.linalg.norm(normal) # 归一化
        
        # 缓存结果
        self._normal_cache[point_key] = normalized_normal
        
        return normalized_normal

    def get_surface_normals_batch(self, points_3d):
        """批量获取多个点的表面法线，性能优化版本"""
        if points_3d is None or len(points_3d) == 0:
            return np.array([])
            
        # 批量查询最近顶点
        _, indices = self.vertex_tree.query(points_3d, k=1)
        normals = self.mesh.vertex_normals[indices]
        
        if self.inward_normals:
            normals = -normals
            
        # 批量归一化
        norms = np.linalg.norm(normals, axis=1, keepdims=True)
        norms[norms == 0] = 1  # 避免除零
        normalized_normals = normals / norms
        
        return normalized_normals

    def interpolate_path_points(self, points, max_segment_length):
        """
        对路径点进行插值，确保相邻点之间的距离不超过指定的最大长度。
        参数:
        points: numpy数组，形状为(N, D)，原始路径点坐标
        max_segment_length: 允许的线段最大长度
        返回:
        numpy数组，包含原始点和插入的插值点
        """
        if points is None or len(points) < 2 or max_segment_length <= 0:
            return points
            
        interpolated_points = []
        for i in range(len(points) - 1):
            p1 = points[i]
            p2 = points[i + 1]
            segment_length = np.linalg.norm(p2 - p1)
            interpolated_points.append(p1)
            if segment_length > max_segment_length:
                num_points_to_insert = int(np.ceil(segment_length / max_segment_length)) - 1
                for j in range(1, num_points_to_insert + 1):
                    t = j / (num_points_to_insert + 1)
                    interp_point = p1 + t * (p2 - p1)
                    interpolated_points.append(interp_point)
        interpolated_points.append(points[-1])
        return np.array(interpolated_points)

    def normal_to_rpy(self, normal):
        """
        将法线向量转换为RPY角(Roll-Pitch-Yaw)
        参数: normal: 3D法线向量 [nx, ny, nz]
        返回: roll, pitch, yaw: 欧拉角(弧度)
        """
        normal = normal / np.linalg.norm(normal)
        nx, ny, nz = normal
        if abs(nz) < 0.999: # 避免法线接近Z轴时的万向锁问题
            aux_vec = np.array([0, 0, 1])
        else:
            aux_vec = np.array([1, 0, 0])
        
        x_axis = np.cross(aux_vec, normal)
        if np.linalg.norm(x_axis) < 1e-6: # 如果法线与辅助向量平行
            # 尝试另一个辅助向量
            aux_vec = np.array([0,1,0]) if abs(normal[1]) < 0.999 else np.array([1,0,0])
            x_axis = np.cross(aux_vec, normal)
        x_axis = x_axis / np.linalg.norm(x_axis)
        y_axis = np.cross(normal, x_axis)
        y_axis = y_axis / np.linalg.norm(y_axis)
        
        R = np.column_stack((x_axis, y_axis, normal))
        
        pitch = np.arcsin(-R[2, 0])
        if abs(np.cos(pitch)) > 1e-9:
            roll = np.arctan2(R[2, 1], R[2, 2])
            yaw = np.arctan2(R[1, 0], R[0, 0])
        else: # 万向锁
            roll = np.arctan2(-R[0, 2], R[1, 1]) # 假设yaw为0或通过其他方式确定
            yaw = 0
        return roll, pitch, yaw

    def normal_to_rpy_degrees(self, normal):
        """将法线向量转换为RPY角(Roll-Pitch-Yaw)，单位为度。"""
        roll_rad, pitch_rad, yaw_rad = self.normal_to_rpy(normal)
        return math.degrees(roll_rad), math.degrees(pitch_rad), math.degrees(yaw_rad)

    def visualize_paths(self, paths_data, show_normals=False, normal_scale=0.5, 
                          normal_hop_distance=None, clearance_above_model_max_viz=None): # 用于可视化的重命名参数
        """可视化生成的路径（边界、投影填充段）以及抬刀路径（抬至模型最大Z之上）。"""
        plt.rcParams['font.sans-serif'] = ['SimHei'] # 设置显示中文的字体
        plt.rcParams['axes.unicode_minus'] = False # 解决负号'-'显示为方块的问题
        fig = plt.figure(figsize=(12, 9))
        ax = fig.add_subplot(111, projection='3d')

        # 移除坐标轴和背景网格，设置背景为白色
        ax.set_axis_off()
        ax.grid(False)
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')

        if not paths_data:
            print("没有路径可供可视化。")
            ax.set_title("未找到路径")
            plt.show()
            return

        all_valid_points_list = []
        boundary_segments = []
        projected_fill_segments = [] # 这现在将保存任一策略的填充段

        for points, normals, is_boundary, segment_id in paths_data:
            if points is None or len(points) < 1:
                continue
            all_valid_points_list.append(points)
            if is_boundary:
                boundary_segments.append((points, normals, segment_id))
            else:
                projected_fill_segments.append((points, normals, segment_id)) # 填充路径（投影或直接偏置）
        
        if not all_valid_points_list:
            print("所有路径段都为空或无效。")
            ax.set_title("未找到有效路径点")
            plt.show()
            return

        all_valid_points = np.vstack(all_valid_points_list)
        min_vals, max_vals = np.min(all_valid_points, axis=0), np.max(all_valid_points, axis=0)
        model_max_z_for_viz = max_vals[2] # 获取模型最大Z值用于可视化跳跃计算

        if self.mesh:
            # 将网格颜色设置为非常浅的灰色，使其在白色背景下几乎不可见，但仍能提供上下文
            ax.plot_trisurf(self.mesh.vertices[:,0], self.mesh.vertices[:,1], self.mesh.vertices[:,2], 
                            triangles=self.mesh.faces, alpha=0.05, color='lightgray', label='原始网格')

        num_boundary_segments = len(boundary_segments)
        if num_boundary_segments > 0:
            print(f"正在绘制 {num_boundary_segments} 个边界路径段...")
            first_boundary_label_added = False
            for i, (points, normals, seg_id) in enumerate(boundary_segments):
                color = 'green' # 统一边界路径颜色
                label = None
                if not first_boundary_label_added:
                    label = "边界路径"
                    first_boundary_label_added = True
                
                ax.plot(points[:,0], points[:,1], points[:,2], color=color, marker='.', markersize=2, linewidth=1.0, label=label)
                if show_normals and normals is not None and len(points) == len(normals):
                    ax.quiver(points[:,0], points[:,1], points[:,2], normals[:,0], normals[:,1], normals[:,2], length=normal_scale, color='lime', arrow_length_ratio=0.3)
        
        num_fill_segments = len(projected_fill_segments)
        if num_fill_segments > 0:
            print(f"正在绘制 {num_fill_segments} 个填充路径段...")
            first_fill_label_added = False
            for i, (points, normals, seg_id) in enumerate(projected_fill_segments):
                color = 'blue' 
                linestyle = '-' 
                label = None
                if not first_fill_label_added:
                    label = "填充路径" # 填充路径的通用标签
                    first_fill_label_added = True
                ax.plot(points[:,0], points[:,1], points[:,2], color=color, linestyle=linestyle, marker='.', markersize=3, linewidth=1.5, label=label)
                if show_normals and normals is not None and len(points) == len(normals):
                    ax.quiver(points[:,0], points[:,1], points[:,2], normals[:,0], normals[:,1], normals[:,2], length=normal_scale, color='magenta', arrow_length_ratio=0.3)

        x_range, y_range, z_range = max_vals - min_vals
        max_range = max(x_range, y_range, z_range, 1.0) # 确保max_range至少为1，避免后续计算问题
        mid_vals = (max_vals + min_vals) / 2
        ax.set_xlim(mid_vals[0] - max_range * 0.6, mid_vals[0] + max_range * 0.6)
        ax.set_ylim(mid_vals[1] - max_range * 0.6, mid_vals[1] + max_range * 0.6)
        ax.set_zlim(mid_vals[2] - max_range * 0.6, mid_vals[2] + max_range * 0.6)
        ax.set_box_aspect([1,1,1]) # 设置坐标轴等比例
        ax.set_xlabel('X (mm)'); ax.set_ylabel('Y (mm)'); ax.set_zlabel('Z (mm)')
        ax.set_title('3D 路径可视化 (含抬刀)') 
        
        # 如果提供了参数，则绘制移动路径
        if len(paths_data) > 1 and normal_hop_distance is not None and clearance_above_model_max_viz is not None:
            print(f"正在绘制 {len(paths_data) - 1} 个路径段间的抬刀和移动路径...")
            first_travel_label_added = False
            travel_color = 'red'
            travel_linestyle = ':' # 虚线表示空程
            travel_linewidth = 0.8

            for i in range(len(paths_data) - 1):
                current_segment_tuple = paths_data[i]
                next_segment_tuple = paths_data[i+1]

                current_points, current_normals, _, current_seg_id = current_segment_tuple
                next_points, _, _, next_seg_id = next_segment_tuple

                if current_points is None or len(current_points) < 1 or \
                   current_normals is None or len(current_normals) != len(current_points):
                    print(f"  可视化: 跳过从段 {current_seg_id} 出发的抬刀路径，因当前段数据无效。")
                    continue
                
                if next_points is None or len(next_points) < 1:
                    print(f"  可视化: 跳过到段 {next_seg_id} 的抬刀路径，因下一段数据无效。")
                    continue

                p1_end_of_print = current_points[-1] # 当前打印段的结束点
                last_normal = current_normals[-1] # 当前打印段结束点的法线
                travel_sequence_points = [p1_end_of_print] # 初始化移动序列点

                # 1. 沿工具轴抬刀 (法线方向跳跃)
                p2_after_tool_axis_hop = p1_end_of_print # 默认情况下，如果没有法线跳跃，则点不变
                tool_axis_hop_target = p1_end_of_print 
                if normal_hop_distance > 0 and last_normal is not None:
                    norm_val = np.linalg.norm(last_normal)
                    if norm_val > 1e-6:
                        # 工具轴方向：如果法线朝内，则工具轴是-normal；否则是normal
                        tool_axis_direction_viz = -last_normal if self.inward_normals else last_normal
                        tool_axis_unit_viz = tool_axis_direction_viz / norm_val
                        tool_axis_hop_target = p1_end_of_print + tool_axis_unit_viz * normal_hop_distance
                travel_sequence_points.append(tool_axis_hop_target)
                p_current_viz = tool_axis_hop_target # 更新当前点
                
                # 2. 全局Z轴抬刀至模型最大Z值以上
                target_z_secondary_absolute_viz = model_max_z_for_viz + clearance_above_model_max_viz
                p3_after_global_z_hop = np.array([p_current_viz[0], 
                                                  p_current_viz[1],
                                                  target_z_secondary_absolute_viz])
                travel_sequence_points.append(p3_after_global_z_hop)
                p_current_viz = p3_after_global_z_hop # 更新当前点
                
                # 准备移动到下一段的预接近点
                p_next_segment_start_print = next_points[0] # 下一段打印的起始点
                _, next_segment_normals_data, _, _ = next_segment_tuple # 下一段的法线数据
                
                if next_segment_normals_data is None or len(next_segment_normals_data) == 0:
                    print(f"  可视化: 跳过到段 {next_seg_id} 的接近路径，因下一段法线数据无效或缺失。")
                    continue 
                
                next_segment_start_normal_raw_viz = next_segment_normals_data[0] # 下一段起始点的原始法线
                pre_approach_point_next_seg_viz = p_next_segment_start_print # 默认预接近点是打印起始点
                if normal_hop_distance > 0 and next_segment_start_normal_raw_viz is not None:
                    norm_val_next_start = np.linalg.norm(next_segment_start_normal_raw_viz)
                    if norm_val_next_start > 1e-6:
                        # 接近时的工具轴：如果inward_normals为True，工具轴为-normal。否则，工具轴为normal。
                        tool_axis_next_start_viz = -next_segment_start_normal_raw_viz if self.inward_normals else next_segment_start_normal_raw_viz
                        tool_axis_unit_next_start_viz = tool_axis_next_start_viz / norm_val_next_start
                        pre_approach_point_next_seg_viz = p_next_segment_start_print + tool_axis_unit_next_start_viz * normal_hop_distance
                
                # 3. 在安全高度移动到下一段预接近点的XY位置
                p4_at_pre_approach_xy_high_z = np.array([pre_approach_point_next_seg_viz[0],
                                                         pre_approach_point_next_seg_viz[1],
                                                         p_current_viz[2]]) # 使用当前的Z安全高度
                travel_sequence_points.append(p4_at_pre_approach_xy_high_z)
                
                # 4. 沿工具轴下降到下一段的预接近点
                travel_sequence_points.append(pre_approach_point_next_seg_viz)
                
                # 5. 移动到下一段的打印起始点
                travel_sequence_points.append(p_next_segment_start_print)
                
                travel_pts_np = np.array(travel_sequence_points)
                ax.plot(travel_pts_np[:,0], travel_pts_np[:,1], travel_pts_np[:,2],
                        color=travel_color, linestyle=travel_linestyle, linewidth=travel_linewidth,
                        label="抬刀/移动路径" if not first_travel_label_added else None, marker='x', markersize=2)
                if not first_travel_label_added and len(travel_pts_np)>0 :
                    first_travel_label_added = True
        
        handles, labels = ax.get_legend_handles_labels()
        by_label = dict(zip(labels, handles)) # 去除重复图例
        if by_label:
            ax.legend(by_label.values(), by_label.keys(), loc='upper left', bbox_to_anchor=(1,1))
        
        plt.tight_layout(rect=[0,0,0.85,1]) # 调整布局以适应图例
        plt.show()

    def save_paths_to_file(self, paths_data, output_file):
        """将路径数据保存到文件。"""
        if not paths_data:
            print("警告: save_paths_to_file 没有路径数据可写。")
            return

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 直接投影切片路径\n")
            f.write(f"# 目标表面距离 (2D填充参考): {self.d_surf}mm\n")
            f.write(f"# 总路径段数: {len(paths_data)}\n")
            f.write(f"# 原始网格: {self.mesh_path}\n")
            f.write(f"# 格式: 点ID X Y Z Roll Pitch Yaw 路径ID 路径类型\n\n")
            
            file_path_id_counter = 0 # 用于文件中路径段的唯一ID
            for points, normals, is_boundary, original_segment_id in paths_data:
                if points is None or len(points) < self.min_points_req:
                    print(f"  保存时跳过无效或过短的路径段 (ID: {original_segment_id})，点数: {len(points) if points is not None else 'None'}")
                    continue
                
                file_path_id_counter += 1
                path_type_str = "BOUNDARY" if is_boundary else f"FILL_SEGMENT_{original_segment_id}" # 路径类型字符串
                
                if normals is None or len(normals) != len(points):
                    print(f"警告: 路径段 {original_segment_id} 缺少或法线数量不匹配点数，将尝试重新计算。")
                    temp_normals = []
                    for p_idx in range(len(points)):
                        temp_normals.append(self.get_surface_normal_at_point(points[p_idx]))
                    used_normals = np.array(temp_normals)
                else:
                    used_normals = normals

                f.write(f"# PathSegment {file_path_id_counter}, OriginalID: {original_segment_id}, Type: {path_type_str}, Points: {len(points)}\n")
                for j, p_coord in enumerate(points):
                    current_normal = used_normals[j]
                    roll, pitch, yaw = self.normal_to_rpy(current_normal) # 弧度
                    path_type_numeric = 1 if is_boundary else 0 # 路径类型数字表示 (1:边界, 0:填充)
                    f.write(f"{j} {p_coord[0]:.6f} {p_coord[1]:.6f} {p_coord[2]:.6f} {roll:.6f} {pitch:.6f} {yaw:.6f} {file_path_id_counter} {path_type_numeric}\n")
                f.write("\n")
        print(f"路径已保存到 {output_file}")

    def get_boundary_edges(self):
        """提取网格的所有独立边界路径的原始边。"""
        boundary_edges_tuples = [] # 存储边界边的元组列表
        edge_map = {} # 边到面的映射
        for face_idx, face in enumerate(self.mesh.faces):
            edges_in_face = [ # 面中的三条边，顶点索引排序
                tuple(sorted((face[0], face[1]))),
                tuple(sorted((face[1], face[2]))),
                tuple(sorted((face[2], face[0])))
            ]
            for edge in edges_in_face:
                if edge in edge_map:
                    edge_map[edge].append(face_idx)
                else:
                    edge_map[edge] = [face_idx]
        
        # 边界边只与一个面相邻
        for edge, face_indices in edge_map.items():
            if len(face_indices) == 1:
                boundary_edges_tuples.append(edge) 
        
        if not boundary_edges_tuples:
            return []
        return boundary_edges_tuples

    def sort_boundary_edges(self, edges_tuples_list):
        """将未排序的边界边元组列表组织成一个或多个有序的边路径。"""
        if not edges_tuples_list:
            return []
        adj = {} # 邻接表：顶点 -> 包含该顶点的边的索引列表
        for i, (v1, v2) in enumerate(edges_tuples_list):
            adj.setdefault(v1, []).append(i)
            adj.setdefault(v2, []).append(i)

        all_ordered_paths_edges = [] # 存储所有排序后的路径 (每条路径是一个边列表)
        visited_edges = [False] * len(edges_tuples_list) # 标记边是否已被访问

        for i in range(len(edges_tuples_list)): # 遍历所有边，尝试从每个未访问的边开始构建路径
            if not visited_edges[i]:
                current_path_edge_indices = [] # 当前路径的边索引列表
                start_edge_index = i
                current_path_edge_indices.append(start_edge_index)
                visited_edges[start_edge_index] = True
                v_left, v_right = edges_tuples_list[start_edge_index] # 当前路径的两个端点
                
                # 从 v_right 端点开始延伸路径
                tip_to_extend = v_right
                while True:
                    extended = False
                    for edge_idx_candidate in adj.get(tip_to_extend, []):
                        if not visited_edges[edge_idx_candidate]:
                            current_path_edge_indices.append(edge_idx_candidate)
                            visited_edges[edge_idx_candidate] = True
                            next_edge = edges_tuples_list[edge_idx_candidate]
                            # 更新下一个要延伸的端点
                            tip_to_extend = next_edge[0] if next_edge[1] == tip_to_extend else next_edge[1]
                            extended = True
                            break
                    if not extended: # 无法再延伸
                        break
                
                # 从 v_left 端点开始反向延伸路径
                tip_to_extend = v_left
                while True:
                    extended = False
                    for edge_idx_candidate in adj.get(tip_to_extend, []):
                        if not visited_edges[edge_idx_candidate]:
                            current_path_edge_indices.insert(0, edge_idx_candidate) # 插入到路径的开头
                            visited_edges[edge_idx_candidate] = True
                            next_edge = edges_tuples_list[edge_idx_candidate]
                            tip_to_extend = next_edge[0] if next_edge[1] == tip_to_extend else next_edge[1]
                            extended = True
                            break
                    if not extended:
                        break
                
                if current_path_edge_indices:
                    ordered_edges_for_this_path = [edges_tuples_list[idx] for idx in current_path_edge_indices]
                    all_ordered_paths_edges.append(ordered_edges_for_this_path)
        return all_ordered_paths_edges

    def visualize_2d_fill(self, original_contour_2d, offset_polygon_shapely, fill_points_2d, connection_types, row_spacing=None):
        """可视化2D填充过程，包括原始轮廓、偏置轮廓和生成的填充点。"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        fig, ax = plt.subplots(figsize=(10, 8))

        # 移除坐标轴和背景网格，设置背景为白色
        ax.set_axis_off()
        ax.grid(False)
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')

        # 绘制原始2D轮廓
        if original_contour_2d is not None and len(original_contour_2d) > 0:
            closed_original_contour = np.vstack([original_contour_2d, original_contour_2d[0]])
            ax.plot(closed_original_contour[:, 0], closed_original_contour[:, 1], 'b-', label='原始边界轮廓', linewidth=1.5)

        if offset_polygon_shapely is not None and not offset_polygon_shapely.is_empty:
            if offset_polygon_shapely.geom_type == 'Polygon':
                offset_exterior_coords = np.array(offset_polygon_shapely.exterior.coords)
                ax.plot(offset_exterior_coords[:, 0], offset_exterior_coords[:, 1], 'g--', label='内缩后填充区域', linewidth=1.5)
                for interior in offset_polygon_shapely.interiors:
                    offset_interior_coords = np.array(interior.coords)
                    ax.plot(offset_interior_coords[:, 0], offset_interior_coords[:, 1], 'g--')
            elif offset_polygon_shapely.geom_type == 'MultiPolygon':
                first_poly_in_multi = True
                for poly in offset_polygon_shapely.geoms:
                    offset_exterior_coords = np.array(poly.exterior.coords)
                    current_label = ""
                    if first_poly_in_multi:
                        current_label = '内缩后填充区域 (多边形)'
                        first_poly_in_multi = False
                    ax.plot(offset_exterior_coords[:, 0], offset_exterior_coords[:, 1], 'g--', label=current_label, linewidth=1.5)
                    for interior in poly.interiors:
                        offset_interior_coords = np.array(interior.coords)
                        ax.plot(offset_interior_coords[:, 0], offset_interior_coords[:, 1], 'g--')
        
        if fill_points_2d is not None and len(fill_points_2d) > 0:
            ax.plot(fill_points_2d[:, 0], fill_points_2d[:, 1], '.', color='purple', markersize=4, label='路径点')
            has_virtual_label = False
            has_fill_label = False
            if connection_types is not None and len(connection_types) == len(fill_points_2d):
                for i in range(1, len(fill_points_2d)):
                    p_prev = fill_points_2d[i-1]
                    p_curr = fill_points_2d[i]
                    is_virtual_segment = (connection_types[i] == 1)
                    if is_virtual_segment:
                        linestyle = '--'
                        color = 'red'
                        current_label = '虚拟连接 (空程)' if not has_virtual_label else None
                        has_virtual_label = True
                    else: 
                        linestyle = '-'
                        color = 'blue'
                        current_label = '实际填充路径' if not has_fill_label else None
                        has_fill_label = True
                    ax.plot([p_prev[0], p_curr[0]], [p_prev[1], p_curr[1]],
                            color=color, linestyle=linestyle, linewidth=1.2, label=current_label)
            else:
                ax.plot(fill_points_2d[:, 0], fill_points_2d[:, 1], 'm.-', label='2D 填充路径 (无类型信息)', markersize=3, linewidth=1)

        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        title = '2D 填充路径可视化'
        if row_spacing is not None:
            title += f' (行距: {row_spacing:.2f}mm)'
        ax.set_title(title)
        ax.legend(loc='best')
        ax.axis('equal')
        plt.grid(True, linestyle=':', alpha=0.7)
        plt.tight_layout()
        plt.show()

    def generate_2d_raster_fill(self, contour_2d_points, row_spacing, offset_distance=None, max_segment_length=None, inner_contours_list=None):
        """
        在给定的二维轮廓内生成Z字形（光栅）填充路径，同时考虑内部孔洞。
        优化版本：减少重复的几何运算，提高性能。
        参数:
        contour_2d_points: (N, 2) numpy数组，XOY平面上的闭合轮廓顶点。
        row_spacing: 填充线间距。
        offset_distance: 可选，向内偏移距离。默认: row_spacing / 2.0。
        max_segment_length: 可选，填充线段最大长度，用于插值。
        inner_contours_list: 可选，内部轮廓（孔洞）的列表，每个元素为(N, 2) numpy数组。
        返回:
        (fill_points_2d, connection_types, offset_polygon_shapely) 或 (None, None, None) 如果失败。
        fill_points_2d: (M, 2) numpy数组，生成的2D填充点。
        connection_types: (M,) list，标记每个点如何连接到前一个点 (0=填充, 1=空程)。
        offset_polygon_shapely: 内缩后的Shapely多边形对象。
        """
        if contour_2d_points is None or len(contour_2d_points) < 3 or row_spacing <= 0:
            print("错误: 轮廓无效或行距不正确，无法生成2D填充。")
            return None, None, None

        try:
            # 创建外部轮廓多边形，优化几何验证
            exterior_polygon = Polygon(contour_2d_points)
            if not exterior_polygon.is_valid:
                exterior_polygon = exterior_polygon.buffer(0)
                if not exterior_polygon.is_valid or exterior_polygon.is_empty:
                    print("错误: 外部轮廓几何无效或为空。")
                    return None, None, None
            
            # 处理内部轮廓（孔洞）- 预先验证以避免重复检查
            valid_holes = []
            if inner_contours_list:
                for inner_contour in inner_contours_list:
                    if inner_contour is not None and len(inner_contour) >= 3:
                        try:
                            inner_poly = Polygon(inner_contour)
                            if not inner_poly.is_valid:
                                inner_poly = inner_poly.buffer(0)
                            if (inner_poly.is_valid and not inner_poly.is_empty and 
                                exterior_polygon.contains(inner_poly)):
                                valid_holes.append(inner_contour)
                        except Exception as inner_e:
                            print(f"警告: 处理内部轮廓时出错: {inner_e}")
            
            # 创建包含孔洞的多边形
            if valid_holes:
                polygon = Polygon(contour_2d_points, holes=valid_holes)
                print(f"创建了包含 {len(valid_holes)} 个孔洞的多边形用于填充。")
            else:
                polygon = exterior_polygon
                print("没有发现有效的内部孔洞，使用普通外部轮廓进行填充。")
            
            if not polygon.is_valid:
                polygon = polygon.buffer(0)
                if not polygon.is_valid or polygon.is_empty:
                    print("错误: 最终多边形（含孔洞）几何无效或为空。")
                    return None, None, None

            actual_offset = offset_distance if offset_distance is not None else row_spacing / 2.0
            actual_offset = -abs(actual_offset)
            offset_polygon = polygon.buffer(actual_offset, join_style=2)
            if not offset_polygon.is_valid or offset_polygon.is_empty:
                print(f"警告: 轮廓内缩 {actual_offset:.2f} 后无效或为空。")
                return None, None, None
        except Exception as e:
            print(f"错误: 创建Shapely Polygon或内缩时出错: {e}")
            return None, None, None

        step_axis = self.axis_index
        scan_axis = 1 - step_axis

        minx, miny, maxx, maxy = polygon.bounds
        min_step_val = miny if step_axis == 1 else minx
        max_step_val = maxy if step_axis == 1 else maxx
        scan_start_val = minx if scan_axis == 0 else miny
        scan_end_val = maxx if scan_axis == 0 else maxy
        scan_extension = row_spacing * 0.1

        all_scan_segments = []
        current_step_val = min_step_val + (row_spacing / 2.0) + 0.001
        
        # 预计算扫描线数量以优化内存分配
        num_scan_lines = int((max_step_val - current_step_val) / row_spacing) + 1
        
        while current_step_val < max_step_val:
            if step_axis == 1:
                scan_line = LineString([(scan_start_val - scan_extension, current_step_val), 
                                        (scan_end_val + scan_extension, current_step_val)])
            else:
                scan_line = LineString([(current_step_val, scan_start_val - scan_extension), 
                                        (current_step_val, scan_end_val + scan_extension)])
            try:
                intersection = offset_polygon.intersection(scan_line)
                if not intersection.is_empty:
                    if intersection.geom_type == 'LineString':
                        if intersection.length > 1e-6: 
                            all_scan_segments.append((intersection, current_step_val))
                    elif intersection.geom_type == 'MultiLineString':
                        for line_part in intersection.geoms:
                            if line_part.length > 1e-6: 
                                all_scan_segments.append((line_part, current_step_val))
            except Exception as e:
                print(f"警告: 步进 {current_step_val:.2f} 计算交集时出错: {e}")
            current_step_val += row_spacing

        if not all_scan_segments:
            print("警告: 未能在轮廓内生成任何有效填充线段。")
            return np.array([]), [], offset_polygon

        all_scan_segments.sort(key=lambda item: item[1])
        
        # 优化：预分配列表容量
        estimated_points = len(all_scan_segments) * 10  # 估算点数
        fill_points_list = []
        fill_points_list_extend = fill_points_list.extend  # 局部引用优化
        connection_types = []
        connection_types_extend = connection_types.extend  # 局部引用优化
        
        direction_forward = True
        total_pts_segments_before_interp, total_pts_segments_after_interp = 0, 0

        for seg_idx, (segment_geom, step_coord) in enumerate(all_scan_segments):
            coords_shapely = list(segment_geom.coords)
            seg_orig_pt_count = len(coords_shapely)
            total_pts_segments_before_interp += seg_orig_pt_count

            if (direction_forward and coords_shapely[0][scan_axis] > coords_shapely[-1][scan_axis]) or \
               (not direction_forward and coords_shapely[0][scan_axis] < coords_shapely[-1][scan_axis]):
                coords_shapely.reverse()
            
            current_segment_pts_np = np.array(coords_shapely)
            
            if max_segment_length is not None and max_segment_length > 0 and len(current_segment_pts_np) > 1:
                interpolated_pts_np = self.interpolate_path_points(current_segment_pts_np, max_segment_length)
                points_for_current_path_segment = interpolated_pts_np.tolist()
                types_for_current_path_segment = [0] * len(points_for_current_path_segment)
            else:
                points_for_current_path_segment = current_segment_pts_np.tolist()
                types_for_current_path_segment = [0] * len(points_for_current_path_segment)
            total_pts_segments_after_interp += len(points_for_current_path_segment)

            if not points_for_current_path_segment: 
                continue

            if fill_points_list:
                last_pt_in_main_list = np.array(fill_points_list[-1])
                first_pt_current_segment = np.array(points_for_current_path_segment[0])
                
                fill_points_list.append(points_for_current_path_segment[0])
                distance_to_new_segment_start = np.linalg.norm(first_pt_current_segment - last_pt_in_main_list)
                is_virtual_connection_to_start = distance_to_new_segment_start > (1.5 * row_spacing)
                connection_types.append(1 if is_virtual_connection_to_start else 0)

                fill_points_list_extend(points_for_current_path_segment[1:])
                connection_types_extend([0] * (len(points_for_current_path_segment) - 1))
            else:
                fill_points_list_extend(points_for_current_path_segment)
                connection_types_extend(types_for_current_path_segment)
            
            direction_forward = not direction_forward

        if not fill_points_list:
            print("警告: 构建Z字形路径后列表为空。")
            return np.array([]), [], offset_polygon

        final_fill_points_2d = np.array(fill_points_list)
        print(f"最终2D光栅填充含 {len(final_fill_points_2d)} 点 (含连接点), 虚拟连接点 {connection_types.count(1)} 个。")
        return final_fill_points_2d, connection_types, offset_polygon

    def _calculate_adjusted_spacing_parameters(self, contour_2d_points, target_3d_row_spacing, target_3d_offset_from_boundary, num_samples=10):
        """
        计算调整后的2D填充参数，以尝试在3D曲面上实现更均匀的间距。
        参数:
        contour_2d_points: (N, 2) numpy数组，XOY平面上的闭合轮廓顶点。
        target_3d_row_spacing: 期望在3D曲面上的行间距。
        target_3d_offset_from_boundary: 期望在3D曲面上的边界偏移。
        num_samples: 用于估算调整因子的采样点/线数量。
        返回:
        (adjusted_2d_row_spacing, adjusted_2d_offset_from_boundary): 调整后的2D行距和2D边界偏移。
        """
        if contour_2d_points is None or len(contour_2d_points) < 3 or target_3d_row_spacing <= 0:
            return target_3d_row_spacing, target_3d_offset_from_boundary # 返回原始值如果输入无效

        try:
            polygon_2d = Polygon(contour_2d_points)
            if not polygon_2d.is_valid:
                polygon_2d = polygon_2d.buffer(0) # 尝试修复
            if not polygon_2d.is_valid or polygon_2d.is_empty:
                return target_3d_row_spacing, target_3d_offset_from_boundary
        except Exception:
            return target_3d_row_spacing, target_3d_offset_from_boundary

        min_x, min_y, max_x, max_y = polygon_2d.bounds
        step_axis = self.axis_index # 步进轴 (0=X, 1=Y)
        scan_axis = 1 - self.axis_index # 扫描轴
        
        # 在步进轴上选择采样点位置的比例
        current_sampling_proportions = [0.2, 0.5, 0.7, 0.9] 
        all_step_coords_for_sampling = [] # 存储步进轴上的采样坐标

        if step_axis == 0: # X轴步进
            for prop in current_sampling_proportions:
                all_step_coords_for_sampling.append(min_x + (max_x - min_x) * prop)
            step_min_extent, step_max_extent = min_x, max_x # 步进轴范围
            scan_start_coord, scan_end_coord = min_y, max_y # 扫描轴范围
        else: # Y轴步进
            for prop in current_sampling_proportions:
                all_step_coords_for_sampling.append(min_y + (max_y - min_y) * prop)
            step_min_extent, step_max_extent = min_y, max_y
            scan_start_coord, scan_end_coord = min_x, max_x
        
        # 如果轮廓在步进轴上的范围足够大，则在两端附近也添加采样点
        if (step_max_extent - step_min_extent) > 2 * target_3d_row_spacing:
            point_near_min_extent = step_min_extent + target_3d_row_spacing 
            point_near_max_extent = step_max_extent - target_3d_row_spacing
            if point_near_min_extent < (all_step_coords_for_sampling[0] if all_step_coords_for_sampling else step_max_extent):
                 all_step_coords_for_sampling.append(point_near_min_extent)
            if point_near_max_extent > (all_step_coords_for_sampling[-1] if all_step_coords_for_sampling else step_min_extent): 
                 all_step_coords_for_sampling.append(point_near_max_extent)

        all_step_coords_for_sampling = sorted(list(set(all_step_coords_for_sampling))) # 去重并排序
        
        all_measured_3d_distances_across_regions = [] # 存储所有测量的3D距离
        region_data_for_weighting = [] # 存储每个区域的加权数据
        
        if not all_step_coords_for_sampling:
            return target_3d_row_spacing, target_3d_offset_from_boundary

        for region_idx, center_step_coord_local in enumerate(all_step_coords_for_sampling):
            # 定义两条平行的采样线，它们在步进轴上的坐标相差 target_3d_row_spacing
            p1_step = center_step_coord_local - target_3d_row_spacing / 2
            p2_step = center_step_coord_local + target_3d_row_spacing / 2

            if step_axis == 0: # X轴步进, Y轴扫描
                current_sample_points_2d_line1 = np.array([[p1_step, y] for y in np.linspace(scan_start_coord, scan_end_coord, num_samples)])
                current_sample_points_2d_line2 = np.array([[p2_step, y] for y in np.linspace(scan_start_coord, scan_end_coord, num_samples)])
            else: # Y轴步进, X轴扫描
                current_sample_points_2d_line1 = np.array([[x, p1_step] for x in np.linspace(scan_start_coord, scan_end_coord, num_samples)])
                current_sample_points_2d_line2 = np.array([[x, p2_step] for x in np.linspace(scan_start_coord, scan_end_coord, num_samples)])

            # 筛选出两条线上都在原始2D轮廓内的点对
            valid_indices_line1 = [idx for idx, p_val in enumerate(current_sample_points_2d_line1) if polygon_2d.contains(Point(p_val))]
            valid_indices_line2 = [idx for idx, p_val in enumerate(current_sample_points_2d_line2) if polygon_2d.contains(Point(p_val))]
            common_valid_indices = sorted(list(set(valid_indices_line1) & set(valid_indices_line2))) # 两条线上都有效的点索引

            if not common_valid_indices or len(common_valid_indices) < 2: # 需要至少两个点对来估算
                continue

            filtered_points_2d_line1_local = current_sample_points_2d_line1[common_valid_indices]
            filtered_points_2d_line2_local = current_sample_points_2d_line2[common_valid_indices]

            # 将筛选后的2D点投影到3D表面
            projected_3d_line1_local, _, success_mask1_local = self.project_2d_points_to_3d_surface(filtered_points_2d_line1_local)
            projected_3d_line2_local, _, success_mask2_local = self.project_2d_points_to_3d_surface(filtered_points_2d_line2_local)

            measured_3d_distances_local = [] # 存储这个区域内测量的3D距离
            num_successful_pairs_in_region = 0
            for k_idx in range(len(projected_3d_line1_local)): # 遍历成功投影的点对
                if success_mask1_local[k_idx] and success_mask2_local[k_idx]:
                    p3d_1 = projected_3d_line1_local[k_idx]
                    p3d_2 = projected_3d_line2_local[k_idx]
                    dist_3d = np.linalg.norm(p3d_1 - p3d_2) # 计算3D距离
                    measured_3d_distances_local.append(dist_3d)
                    num_successful_pairs_in_region +=1
            
            if measured_3d_distances_local:
                avg_dist_local = np.mean(measured_3d_distances_local) # 这个区域的平均3D距离
                all_measured_3d_distances_across_regions.extend(measured_3d_distances_local) 
                region_data_for_weighting.append({
                    'avg_dist': avg_dist_local, 
                    'num_pairs': num_successful_pairs_in_region, 
                    'step_coord': center_step_coord_local
                })

        if not region_data_for_weighting: # 如果没有收集到任何有效的区域数据
            return target_3d_row_spacing, target_3d_offset_from_boundary

        sum_weighted_avg_dist = 0.0
        sum_weights = 0.0
        epsilon = 1e-6 # 用于比较浮点数的小值
        
        processed_regional_weights = []
        for r_data in region_data_for_weighting:
            avg_dist_r = r_data['avg_dist']
            deviation = abs(avg_dist_r - target_3d_row_spacing) # 与目标3D行距的偏差
            weight = deviation # 使用偏差作为权重 (偏差越大，权重越大，意味着这个区域的平均距离对最终加权平均的影响更大)
            processed_regional_weights.append({'step_coord': r_data['step_coord'], 'avg_dist': avg_dist_r, 'deviation': deviation, 'weight': weight, 'num_pairs':r_data['num_pairs']})
            sum_weighted_avg_dist += avg_dist_r * weight
            sum_weights += weight

        avg_measured_3d_spacing = target_3d_row_spacing # 默认值
        if sum_weights < epsilon: # 如果总权重过小 (例如，所有区域的平均距离都非常接近目标距离)
            if all_measured_3d_distances_across_regions:
                avg_measured_3d_spacing = np.mean(all_measured_3d_distances_across_regions) # 使用简单平均值
        else:
            weighted_avg_measured_3d_spacing = sum_weighted_avg_dist / sum_weights # 加权平均的3D间距
            avg_measured_3d_spacing = weighted_avg_measured_3d_spacing 
        
        if avg_measured_3d_spacing < 1e-6: # 避免除以零或过小的值
            return target_3d_row_spacing, target_3d_offset_from_boundary

        # 计算调整因子
        adjustment_factor = target_3d_row_spacing / avg_measured_3d_spacing
        adjustment_factor = np.clip(adjustment_factor, 0.5, 2.0) # 限制调整因子的范围，防止过度调整
        
        adjusted_2d_row_spacing = target_3d_row_spacing * adjustment_factor
        adjusted_2d_offset_from_boundary = target_3d_offset_from_boundary * adjustment_factor

        print(f"  参数调整(投影策略): 目标3D行距 {target_3d_row_spacing:.3f}, 实测平均3D间距 {avg_measured_3d_spacing:.3f}, 调整因子 {adjustment_factor:.3f}")
        print(f"  调整后2D行距: {adjusted_2d_row_spacing:.3f}, 调整后2D偏移: {adjusted_2d_offset_from_boundary:.3f}")

        return adjusted_2d_row_spacing, adjusted_2d_offset_from_boundary

    def generate_2d_concentric_fill(self, contour_2d_points, row_spacing, offset_distance=None, max_segment_length=None, inner_contours_list=None):
        """
        在给定的二维轮廓内生成同心圆填充路径，同时考虑内部孔洞。
        参数与 generate_2d_raster_fill 类似。
        inner_contours_list: 可选，内部轮廓（孔洞）的列表，每个元素为(N, 2) numpy数组。
        返回:
        (fill_points_2d, connection_types, initial_offset_polygon) 或 (None, None, None) 如果失败。
        fill_points_2d: (M, 2) numpy数组，生成的2D填充点。
        connection_types: (M,) list，标记每个点如何连接到前一个点 (0=填充, 1=空程)。
        initial_offset_polygon: 初始内缩后的Shapely多边形对象。
        """
        if contour_2d_points is None or len(contour_2d_points) < 3 or row_spacing <= 0:
            print("错误: 轮廓无效或行距不正确，无法生成2D同心圆填充。")
            return None, None, None

        try:
            # 创建外部轮廓多边形
            exterior_polygon = Polygon(contour_2d_points)
            if not exterior_polygon.is_valid:
                exterior_polygon = exterior_polygon.buffer(0) # 尝试修复
            if not exterior_polygon.is_valid or exterior_polygon.is_empty:
                print("错误: 外部轮廓几何无效或为空，无法进行同心圆填充。")
                return None, None, None
                
            # 处理内部轮廓（孔洞）
            holes = []
            if inner_contours_list:
                for inner_contour in inner_contours_list:
                    if inner_contour is not None and len(inner_contour) >= 3:
                        try:
                            inner_poly = Polygon(inner_contour)
                            if inner_poly.is_valid and not inner_poly.is_empty and exterior_polygon.contains(inner_poly):
                                holes.append(inner_contour)
                            elif not inner_poly.is_valid:
                                fixed_inner = inner_poly.buffer(0)
                                if fixed_inner.is_valid and not fixed_inner.is_empty and exterior_polygon.contains(fixed_inner):
                                    holes.append(inner_contour)
                        except Exception as inner_e:
                            print(f"警告: 处理内部轮廓时出错 (同心圆): {inner_e}")
            
            # 创建包含孔洞的多边形
            if holes:
                original_polygon = Polygon(contour_2d_points, holes=holes)
                print(f"同心圆填充: 创建了包含 {len(holes)} 个孔洞的多边形用于填充。")
            else:
                original_polygon = exterior_polygon
                print("同心圆填充: 没有发现有效的内部孔洞，使用普通外部轮廓进行填充。")
                
            if not original_polygon.is_valid:
                original_polygon = original_polygon.buffer(0) # 尝试再次修复
            if not original_polygon.is_valid or original_polygon.is_empty:
                print("错误: 最终多边形（含孔洞）几何无效或为空，无法进行同心圆填充。")
                return None, None, None

            actual_offset_for_fill_area = offset_distance if offset_distance is not None else row_spacing / 2.0
            actual_offset_for_fill_area = -abs(actual_offset_for_fill_area) # 确保向内偏移
            
            current_fill_polygon = original_polygon.buffer(actual_offset_for_fill_area, join_style=2) # join_style=2 (MITRE)
            if not current_fill_polygon.is_valid or current_fill_polygon.is_empty:
                print(f"警告: 轮廓内缩 {actual_offset_for_fill_area:.2f} (用于填充区域) 后无效或为空。无法生成同心圆填充。")
                return None, None, original_polygon.buffer(0) # 返回原始多边形（修复后）
            
            initial_offset_polygon_for_return = current_fill_polygon.buffer(0) # 保存初始内缩后的多边形

        except Exception as e:
            print(f"错误: 创建Shapely Polygon或初始内缩时出错 (同心圆): {e}")
            return None, None, None

        all_concentric_paths_points = [] # 存储所有同心圆路径点
        all_connection_types = [] # 0表示填充, 1表示到新轮廓起点的空程
        min_area_threshold = (row_spacing**2) / 8 # 启发式阈值，用于提前停止处理非常小的区域
        loop_count = 0
        max_loops = 200 # 防止无限循环

        while current_fill_polygon.is_valid and not current_fill_polygon.is_empty and current_fill_polygon.area > min_area_threshold and loop_count < max_loops:
            loop_count += 1
            
            polygons_to_process_this_iteration = [] # 当前迭代要处理的多边形
            if current_fill_polygon.geom_type == 'Polygon':
                polygons_to_process_this_iteration = [current_fill_polygon]
            elif current_fill_polygon.geom_type == 'MultiPolygon':
                # 如果是多多边形，优先处理面积最大的，也可以扩展为处理所有多边形
                polygons_to_process_this_iteration = sorted(list(current_fill_polygon.geoms), key=lambda p: p.area, reverse=True)
            else: # 其他类型 (例如，如果完全收缩成线串)
                break 

            next_outer_polygons_for_next_iteration = [] # 用于下一个同心壳的多边形
            
            for poly_idx, current_poly_shell in enumerate(polygons_to_process_this_iteration):
                if not current_poly_shell.is_valid or current_poly_shell.is_empty or current_poly_shell.area < min_area_threshold:
                    continue

                contour_coords_np = np.array(current_poly_shell.exterior.coords) # 获取外轮廓坐标
                
                # Shapely的exterior.coords通常是(N+1,2)且首尾点相同
                # 为了插值函数，如果首尾点相同，则移除末尾的重复点
                if np.allclose(contour_coords_np[0], contour_coords_np[-1]):
                    contour_coords_np = contour_coords_np[:-1]

                if len(contour_coords_np) < self.min_points_req : # 插值前至少需要最少点数
                    continue

                # 对轮廓进行插值
                interpolated_contour_np = self.interpolate_path_points(contour_coords_np, max_segment_length)
                
                # 重新闭合路径段数据 (如果interpolate_path_points不闭合它)
                if not np.allclose(interpolated_contour_np[0], interpolated_contour_np[-1]):
                     interpolated_contour_np = np.vstack([interpolated_contour_np, interpolated_contour_np[0]])
                
                if len(interpolated_contour_np) < self.min_points_req: # 插值后再次检查点数
                    continue 
                
                # 添加到路径列表
                if not all_concentric_paths_points: # 如果是第一个路径段
                    all_concentric_paths_points.extend(interpolated_contour_np.tolist())
                    all_connection_types.extend([0] * len(interpolated_contour_np)) # 全是填充
                else:
                    # 添加到新轮廓起点的空程
                    all_concentric_paths_points.append(interpolated_contour_np[0].tolist()) # 新轮廓的第一个点
                    all_connection_types.append(1) # 标记为空程连接
                    # 添加轮廓的其余点
                    all_concentric_paths_points.extend(interpolated_contour_np[1:].tolist())
                    all_connection_types.extend([0] * (len(interpolated_contour_np) - 1)) # 这些是填充

                # 从current_poly_shell生成下一个内层多边形
                inner_poly_candidate = current_poly_shell.buffer(-row_spacing, join_style=2) # join_style=2 (MITRE)
                if inner_poly_candidate.is_valid and not inner_poly_candidate.is_empty and inner_poly_candidate.area > min_area_threshold:
                    if inner_poly_candidate.geom_type == 'Polygon':
                        next_outer_polygons_for_next_iteration.append(inner_poly_candidate)
                    elif inner_poly_candidate.geom_type == 'MultiPolygon':
                        next_outer_polygons_for_next_iteration.extend(list(inner_poly_candidate.geoms))
            
            if not next_outer_polygons_for_next_iteration: # 如果没有有效的内层多边形候选
                break
            
            # 从收集到的有效内层候选多边形重建current_fill_polygon
            # 如果有多个，取面积最大的或将它们合并。为简单起见，取面积最大的。
            valid_next_polys = [p for p in next_outer_polygons_for_next_iteration if p.is_valid and not p.is_empty and p.area > min_area_threshold]
            if not valid_next_polys:
                    break
            current_fill_polygon = max(valid_next_polys, key=lambda p: p.area) # 简化处理：取面积最大的
        
        if not all_concentric_paths_points:
            print("警告: 未能生成任何有效的2D同心圆填充路径点。")
            return np.array([]), [], initial_offset_polygon_for_return # 返回空数组和初始内缩多边形

        final_fill_points_2d = np.array(all_concentric_paths_points)
        print(f"2D同心圆填充路径构建完成，包含 {len(final_fill_points_2d)} 点，{all_connection_types.count(1)} 个环间连接。")
        return final_fill_points_2d, all_connection_types, initial_offset_polygon_for_return

    def project_2d_points_to_3d_surface(self, points_2d):
        """
        将一组2D点（XOY平面）投影到三维网格表面。
        优化版本：使用批量射线投影提高性能。
        参数:
        points_2d: (N, 2) numpy数组，包含要投影的2D点。
        返回:
        (projected_points_3d, corresponding_normals_3d, success_mask)
        projected_points_3d: (M, 3) numpy数组，成功投影的三维点。
        corresponding_normals_3d: (M, 3) numpy数组，对应的表面法线。
        success_mask: (N,) boolean数组，标记哪些原始2D点成功投影。
        """
        if points_2d is None or len(points_2d) == 0:
            return np.array([]), np.array([]), np.array([])

        num_points = len(points_2d)
        # 设置射线起点在网格Z轴范围之上，方向朝下
        z_min, z_max = self.mesh_bounds[0, 2], self.mesh_bounds[1, 2]
        ray_origin_z = z_max + abs(z_max - z_min) + 1.0
        
        # 批量构建射线起点和方向
        ray_origins = np.column_stack([points_2d, np.full(num_points, ray_origin_z)])
        ray_directions = np.tile([0, 0, -1], (num_points, 1))
        
        # 批量射线投影
        locations, index_ray, index_tri = self.mesh.ray.intersects_location(
            ray_origins=ray_origins, 
            ray_directions=ray_directions
        )
        
        # 初始化结果
        projected_points_3d_list = []
        normals_3d_list = []
        success_mask = np.zeros(num_points, dtype=bool)
        
        if len(locations) > 0:
            # 批量处理命中结果
            # 为每个射线索引找到第一个（最近的）交点
            unique_ray_indices = np.unique(index_ray)
            for ray_idx in unique_ray_indices:
                mask = index_ray == ray_idx
                if np.any(mask):
                    # 取该射线的第一个交点（通常是最近的）
                    first_hit_idx = np.where(mask)[0][0]
                    hit_point = locations[first_hit_idx]
                    
                    projected_points_3d_list.append(hit_point)
                    # 使用预计算的法线而不是每次重新计算
                    normal = self.get_surface_normal_at_point(hit_point)
                    normals_3d_list.append(normal)
                    success_mask[ray_idx] = True

        if not projected_points_3d_list:
            return np.array([]), np.array([]), success_mask
            
        return np.array(projected_points_3d_list), np.array(normals_3d_list), success_mask

    def build_final_3d_paths(self, projected_points_3d, projected_normals_3d, success_mask_2d, 
                               original_2d_points, original_connection_types, 
                               boundary_paths_data=None, projected_segment_id_start=-1):
        """
        根据投影后的3D点和原始2D连接类型构建最终的分段3D路径。
        参数:
        projected_points_3d: 成功投影的3D点数组。
        projected_normals_3d: 对应的3D法线数组。
        success_mask_2d: 标记哪些原始2D点成功投影。
        original_2d_points: 原始的2D填充点数组。
        original_connection_types: 原始2D点的连接类型 (0=填充, 1=空程)。
        boundary_paths_data: 可选，从get_projected_boundary_contours获取的边界数据列表。
        projected_segment_id_start: 投影填充段的起始ID。
        返回:
        final_paths_list: 包含边界和投影填充段的路径数据列表。每个元素是 (points_3d, normals_3d, is_boundary, segment_id)。
        """
        final_paths_list = []
        current_segment_id = projected_segment_id_start # 当前填充段的ID

        # 如果提供了边界路径数据，首先添加它们
        if boundary_paths_data:
            for b_data in boundary_paths_data:
                b_points_3d = b_data['points_3d']
                # 使用批量法线计算优化性能
                b_normals_3d = self.get_surface_normals_batch(b_points_3d)
                final_paths_list.append((b_points_3d, b_normals_3d, True, b_data['id']))

        valid_original_2d_indices = np.where(success_mask_2d)[0] # 获取成功投影的原始2D点的索引
        if len(valid_original_2d_indices) == 0 or len(projected_points_3d) == 0:
            print("没有足够的成功投影点来构建3D填充路径。")
            return final_paths_list # 可能只包含边界路径

        current_3d_path_points = [] # 当前正在构建的3D路径段的点
        current_3d_path_normals = [] # 当前路径段的法线
        projected_idx_counter = 0 # 对应于projected_points_3d和projected_normals_3d的计数器

        for i in range(len(original_2d_points)): # 遍历所有原始2D点
            if not success_mask_2d[i]: # 如果原始2D点未成功投影
                # 如果这个未成功投影的点本应是一个空程的起点，并且当前有正在构建的路径段，则结束当前段
                if original_connection_types[i] == 1 and current_3d_path_points: 
                    if len(current_3d_path_points) >= self.min_points_req:
                        final_paths_list.append((np.array(current_3d_path_points), 
                                                 np.array(current_3d_path_normals), 
                                                 False, current_segment_id))
                        current_segment_id -= 1 # 更新下一个填充段的ID
                    current_3d_path_points = [] # 重置当前路径段
                    current_3d_path_normals = []
                continue # 跳过未成功投影的点

            # 获取对应的3D点和法线
            p3d = projected_points_3d[projected_idx_counter]
            n3d = projected_normals_3d[projected_idx_counter]
            # original_connection_types[i] 指的是 original_2d_points[i] 这个点
            # 如果 original_connection_types[i] == 1, 表示这个点是新段的起点 (通过空程到达)
            is_start_of_travel_or_new_segment = (original_connection_types[i] == 1)
            projected_idx_counter += 1 

            if is_start_of_travel_or_new_segment: # 这个点是通过"空程"移动到达的
                if current_3d_path_points: # 结束之前的实际打印段
                    if len(current_3d_path_points) >= self.min_points_req:
                        final_paths_list.append((np.array(current_3d_path_points), 
                                                 np.array(current_3d_path_normals), 
                                                 False, current_segment_id))
                        current_segment_id -= 1
                    current_3d_path_points = [] # 重置
                    current_3d_path_normals = []
                
                # 用这个点开始一个新的段 (它是新填充部分的第一个点)
                current_3d_path_points.append(p3d)
                current_3d_path_normals.append(n3d)
        
            else: # 这个点是正在进行的打印段的一部分
                if not current_3d_path_points: # 如果是整体的第一个点，开始新段
                    current_3d_path_points.append(p3d)
                    current_3d_path_normals.append(n3d)
                else: # 添加到当前段
                    current_3d_path_points.append(p3d)
                    current_3d_path_normals.append(n3d)
        
        # 添加最后一个路径段 (如果有)
        if current_3d_path_points and len(current_3d_path_points) >= self.min_points_req:
            final_paths_list.append((np.array(current_3d_path_points), 
                                     np.array(current_3d_path_normals), 
                                     False, current_segment_id))
        
        num_fill_segments_added = sum(1 for _, _, is_b, _ in final_paths_list if not is_b and (_ if boundary_paths_data is None else True))
        if boundary_paths_data: # 如果是从build_final_3d_paths内部调用（只处理填充）
             num_fill_segments_added = len(final_paths_list)


        print(f"构建了 {num_fill_segments_added} 个3D投影填充段。")
        return final_paths_list

    def compute_boundary_distances_batch(self, points_3d, boundary_linestrings):
        """
        批量计算点到边界的最小距离
        
        参数:
        points_3d: (N, 3) numpy数组，要计算距离的3D点
        boundary_linestrings: Shapely LineString对象列表
        
        返回:
        distances: (N,) numpy数组，每个点到最近边界的距离
        """
        if not boundary_linestrings:
            return np.full(len(points_3d), float('inf'))
        
        distances = np.full(len(points_3d), float('inf'))
        
        for i, point_3d in enumerate(points_3d):
            point_2d = Point(point_3d[:2])  # 使用XY坐标创建2D点
            min_distance = float('inf')
            
            for boundary_line in boundary_linestrings:
                try:
                    dist = boundary_line.distance(point_2d)
                    min_distance = min(min_distance, dist)
                except Exception:
                    continue  # 跳过计算失败的情况
            
            distances[i] = min_distance
        
        return distances

    def _calculate_actual_3d_spacing_between_strip_sets(self, strips_layer1, strips_layer2, scan_axis, num_samples_on_strip1=10):
        """
        Calculates the actual 3D spacing statistics between two sets of 3D path strips.

        Args:
            strips_layer1 (list): List of numpy arrays, where each array is a 3D path strip from the first layer.
            strips_layer2 (list): List of numpy arrays, where each array is a 3D path strip from the second layer.
            scan_axis (int): The axis perpendicular to the offset direction (0 for X, 1 for Y), used to define the measurement plane.
            num_samples_on_strip1 (int): Number of points to sample on each strip of layer 1 for measurement.

        Returns:
            dict: A dictionary with keys 'avg_dist', 'min_dist', 'max_dist', 'median_dist', 'std_dev_dist', 'num_samples'.
                  Returns None if no valid distances could be calculated.
        """
        all_distances_for_this_step = []

        for points_arr1_strip in strips_layer1: # points_arr1_strip 是 Layer 1 中的一个路径条带 (numpy array)
            if len(points_arr1_strip) < 1: 
                continue

            # Ensure num_samples_on_strip1 is at least 1 if strip has points, and not more than available points
            current_num_samples = min(max(1, num_samples_on_strip1), len(points_arr1_strip))
            if len(points_arr1_strip) == 1: # Handle single-point strip
                 sample_indices_strip1 = [0]
            else:
                sample_indices_strip1 = np.linspace(0, len(points_arr1_strip) - 1, current_num_samples, dtype=int, endpoint=True)
            
            sampled_points_from_strip1 = points_arr1_strip[sample_indices_strip1]

            for sample_p1_on_strip1 in sampled_points_from_strip1: # 对于 Layer 1 上的每个采样点 sample_p1_on_strip1
                scan_axis_coord_of_plane = sample_p1_on_strip1[scan_axis] # 定义了测量平面的扫描轴坐标
                intersections_on_layer2_for_this_sample_p1 = []

                for points_arr2_strip in strips_layer2: # points_arr2_strip 是 Layer 2 中的一个路径条带
                    if len(points_arr2_strip) < 2: # 一个条带至少需要2个点才能形成线段
                        continue
                    
                    for j_segment in range(len(points_arr2_strip) - 1): # 遍历 Layer 2 上当前条带的每个线段
                        seg_start_p_l2 = points_arr2_strip[j_segment]
                        seg_end_p_l2 = points_arr2_strip[j_segment + 1]

                        scan_coord_seg_start_l2 = seg_start_p_l2[scan_axis]
                        scan_coord_seg_end_l2 = seg_end_p_l2[scan_axis]
                        
                        intersection_point_l2 = None
                        # 检查线段是否与测量平面平行且在该平面内，或者与平面相交
                        if abs(scan_coord_seg_start_l2 - scan_axis_coord_of_plane) < 1e-6 and \
                           abs(scan_coord_seg_end_l2 - scan_axis_coord_of_plane) < 1e-6:
                            # 线段在测量平面内，可以取线段中点，或者更精确地找到离sample_p1_on_strip1最近的点
                            # For simplicity, if the segment is IN the plane, we might consider both ends
                            # or project sample_p1_on_strip1 onto the segment.
                            # Current approach: take mid-point. A more robust method would find the closest point on this segment to sample_p1_on_strip1
                            intersection_point_l2 = (seg_start_p_l2 + seg_end_p_l2) / 2.0
                        
                        elif (scan_coord_seg_start_l2 < scan_axis_coord_of_plane <= scan_coord_seg_end_l2) or \
                             (scan_coord_seg_end_l2 < scan_axis_coord_of_plane <= scan_coord_seg_start_l2):
                            # 线段与测量平面有交点 (确保分母不为0)
                            if abs(scan_coord_seg_end_l2 - scan_coord_seg_start_l2) > 1e-6:
                                t = (scan_axis_coord_of_plane - scan_coord_seg_start_l2) / (scan_coord_seg_end_l2 - scan_coord_seg_start_l2)
                                t_clipped = np.clip(t, 0.0, 1.0) # Ensure intersection is within the segment
                                intersection_point_l2 = seg_start_p_l2 + t_clipped * (seg_end_p_l2 - seg_start_p_l2)
                        
                        if intersection_point_l2 is not None:
                            intersections_on_layer2_for_this_sample_p1.append(intersection_point_l2)
                            # Don't break here, collect all intersections from ALL strips in layer2 with this plane
                
                if intersections_on_layer2_for_this_sample_p1:
                    min_dist_for_this_sample_p1 = float('inf')
                    closest_p_intersect_l2 = None 
                    for p_intersect_l2 in intersections_on_layer2_for_this_sample_p1:
                        dist = np.linalg.norm(sample_p1_on_strip1 - p_intersect_l2)
                        if dist < min_dist_for_this_sample_p1:
                            min_dist_for_this_sample_p1 = dist
                            closest_p_intersect_l2 = p_intersect_l2
                    
                    if np.isfinite(min_dist_for_this_sample_p1) and closest_p_intersect_l2 is not None:
                        # This is where the DEBUG print was:
                        # print(f"      DEBUG: Layer1 Sample: {sample_p1_on_strip1}, Layer2 Intersection: {closest_p_intersect_l2}, Calculated 3D Dist: {min_dist_for_this_sample_p1:.4f}")
                        all_distances_for_this_step.append(min_dist_for_this_sample_p1)
        
        if all_distances_for_this_step:
            return {
                'avg_dist': np.mean(all_distances_for_this_step),
                'min_dist': np.min(all_distances_for_this_step),
                'max_dist': np.max(all_distances_for_this_step),
                'median_dist': np.median(all_distances_for_this_step),
                'std_dev_dist': np.std(all_distances_for_this_step),
                'num_samples': len(all_distances_for_this_step)
            }
        else:
            return { # Return default/nan if no samples
                'avg_dist': float('nan'), 
                'min_dist': float('nan'), 
                'max_dist': float('nan'), 
                'median_dist': float('nan'), 
                'std_dev_dist': float('nan'), 
                'num_samples': 0
            }

    def _create_direct_offset_paths(self, row_spacing, offset_from_bounds, max_segment_length, 
                                  proximity_threshold=0.15, adaptive_density=True, 
                                  # Removed old adaptive parameters
                                  # min_spacing_factor=0.3, 
                                  # use_normal_component_method=True, 
                                  # ny_calculation_strategy='average', 
                                  # New iteration parameters for generate_adaptive_slice_positions_iterative
                                  iter_min_delta_y_factor=0.05,
                                  iter_max_delta_y_factor=2.0,
                                  iter_tolerance_abs=0.1,
                                  iter_max_iterations_per_step=15,
                                  iter_num_samples_for_spacing_calc=7
                                  # Removed use_even_odd_rule=True
                                  ):
        """
        新策略：直接在3D曲面上通过沿一个轴偏置（切片）并连接路径条带来生成填充。
        如果启用自适应密度，则使用基于迭代反馈的方法 (`generate_adaptive_slice_positions_iterative`) 
        来确定切片位置，以尝试在3D表面上获得更均匀的路径间距。
        
        参数:
        row_spacing: 沿偏置轴的基础路径条带间距。
        offset_from_bounds: 从网格在偏置轴上的边界开始向内偏移的距离。
        max_segment_length: 路径条带上点的最大允许间距。
        proximity_threshold: (当前在此方法中不直接使用进行过滤) 与边界和孔洞的最小距离，主要用于影响传递给迭代切片器的孔洞多边形。
        adaptive_density: 是否启用自适应密度控制 (通过迭代方法)。
        iter_min_delta_y_factor, iter_max_delta_y_factor, iter_tolerance_abs, 
        iter_max_iterations_per_step, iter_num_samples_for_spacing_calc: 控制迭代自适应切片行为的参数。
        
        返回:
        final_paths_list: 包含边界和直接偏置填充段的路径数据列表。
        spacing_analysis_data: 包含间距分析数据的列表 (如果适用)。
        """
        print(f"\n--- 开始执行直接偏置填充策略 ---")
        print(f"  基础行距: {row_spacing:.3f} mm")
        print(f"  边界内缩: {offset_from_bounds:.3f} mm")
        print(f"  最大段长: {max_segment_length:.3f} mm")
        print(f"  自适应密度: {'启用' if adaptive_density else '禁用'}")
        if adaptive_density:
            # Log for iterative method (already updated in previous step if it was part of main changes)
            # This section is now simplified as old methods are removed.
            print(f"  直接偏置策略 - 使用迭代反馈自适应间距方法")
            print(f"    迭代参数: MinΔYFactor={iter_min_delta_y_factor:.2f}, MaxΔYFactor={iter_max_delta_y_factor:.2f}, AbsTol={iter_tolerance_abs:.2f}mm")

        final_paths_list = []
        fill_segment_id_counter = -10000 # 为填充段使用较大的负ID起始值
        
        # 获取投影的边界轮廓并区分内外部
        all_boundary_contour_info = self.get_projected_boundary_contours()
        external_contours_3d = []
        internal_contours_3d = []
        # shapely_boundary_linestrings = [] # 存储所有边界的Shapely LineString对象 # DEPRECATED by all_3d_boundary_linestrings
        shapely_internal_linestrings = [] # 存储内部轮廓的Shapely LineString对象（用于孔洞检测）
        
        # Helper function to convert 3D points to Shapely LineString
        # Moved here to be available before its first use.
        def to_shapely_linestring_3d(points):
            if len(points) < 2:
                return None
            return LineString(points)

        # Prepare 3D boundary polylines (Shapely LineStrings) for intersection logic
        all_3d_boundary_linestrings = []
        if all_boundary_contour_info:
            print(f"  直接偏置策略: 正在准备 {len(all_boundary_contour_info)} 个3D边界轮廓线...")
            for contour_data in all_boundary_contour_info:
                if contour_data['points_3d'] is not None and len(contour_data['points_3d']) >= 2:
                    boundary_poly_3d = to_shapely_linestring_3d(contour_data['points_3d'])
                    if boundary_poly_3d:
                        all_3d_boundary_linestrings.append(boundary_poly_3d)
                    else:
                        print(f"    警告: 未能为轮廓ID {contour_data['id']} 创建3D LineString (点数: {len(contour_data['points_3d'])})")
                else:
                    print(f"    警告: 轮廓ID {contour_data['id']} 没有足够的3D点 (点数: {len(contour_data['points_3d']) if contour_data['points_3d'] is not None else 'None'})")
        
        if not all_3d_boundary_linestrings:
            print("  警告: 直接偏置策略 - 未找到有效的3D边界轮廓线用于交点检测。填充段可能不准确或无法生成。")
        else:
            print(f"  直接偏置策略: 成功准备 {len(all_3d_boundary_linestrings)} 个3D边界轮廓线 (Shapely LineString)。")

        if all_boundary_contour_info: # This block remains for now for external/internal distinction, though its direct use might change
            # 区分内部和外部轮廓
            for contour_data in all_boundary_contour_info:
                contour_2d = contour_data['points_2d']
                contour_poly = None
                try:
                    contour_poly = Polygon(contour_2d)
                    if not contour_poly.is_valid:
                        contour_poly = contour_poly.buffer(0)  # 尝试修复
                except Exception as e:
                    print(f"警告: 处理轮廓 {contour_data['id']} 时出错: {e}")
                    continue
                
                # 检查此轮廓是否包含在其他轮廓内
                is_internal = False
                for other_data in all_boundary_contour_info:
                    if other_data['id'] == contour_data['id']:
                        continue  # 跳过自身比较
                    
                    other_poly = None
                    try:
                        other_poly = Polygon(other_data['points_2d'])
                        if not other_poly.is_valid:
                            other_poly = other_poly.buffer(0)
                        
                        if other_poly.contains(contour_poly):
                            is_internal = True
                            break
                    except Exception as e:
                        print(f"警告: 比较轮廓 {contour_data['id']} 和 {other_data['id']} 时出错: {e}")
                        continue
                
                if is_internal:
                    internal_contours_3d.append(contour_data)
                    # 为内部轮廓创建3D LineString用于穿越检测
                    if len(contour_data['points_3d']) >= 2:
                        shapely_internal_linestrings.append(LineString(contour_data['points_3d']))
                    print(f"直接偏置策略: 轮廓 {contour_data['id']} 识别为内部轮廓（孔洞）")
                else:
                    external_contours_3d.append(contour_data)
                    print(f"直接偏置策略: 轮廓 {contour_data['id']} 识别为外部轮廓")
                    
                # 为所有轮廓创建LineString（用于边界距离检查）
                if len(contour_data['points_3d']) >= 2:
                    # shapely_boundary_linestrings.append(LineString(contour_data['points_3d']))
                    pass
        
        # 1. 添加3D边界路径（包括内部和外部）
        boundary_id_counter = 1
        for contour_data in all_boundary_contour_info:
            b_points_3d = contour_data['points_3d']
            if len(b_points_3d) >= self.min_points_req:
                # 使用批量法线计算优化性能
                b_normals_3d = self.get_surface_normals_batch(b_points_3d)
                final_paths_list.append((b_points_3d, b_normals_3d, True, contour_data['id']))
                boundary_id_counter += 1
            else:
                print(f"直接偏置策略：跳过边界路径 {contour_data['id']}，点数不足 ({len(b_points_3d)})。")
        
        print(f"直接偏置策略：添加了 {len(external_contours_3d)} 个外部轮廓和 {len(internal_contours_3d)} 个内部轮廓。")
        print(f"  为孔洞检测创建了 {len(shapely_internal_linestrings)} 个内部轮廓LineString对象。")
        if all_3d_boundary_linestrings:
            print(f"  为边界路径创建了 {len(all_3d_boundary_linestrings)} 个Shapely LineString对象。")
        else:
            print("  无边界路径用于Shapely邻近检查。")

        # proximity_threshold参数现在从函数参数传入，默认值0.15毫米

        # 2. 预先创建内部轮廓的2D多边形用于孔洞检测
        internal_polygons_2d = []
        if internal_contours_3d:
            print(f"  预先创建内部轮廓的孔洞检测多边形...")
            for internal_contour_data in internal_contours_3d:
                try:
                    internal_poly_2d = Polygon(internal_contour_data['points_2d'])
                    if not internal_poly_2d.is_valid:
                        internal_poly_2d = internal_poly_2d.buffer(0)
                    if internal_poly_2d.is_valid and not internal_poly_2d.is_empty:
                        # 为孔洞多边形添加一个安全缓冲区，与边界距离保持一致
                        safety_buffer = proximity_threshold  # 使用与边界相同的距离
                        buffered_hole_poly = internal_poly_2d.buffer(safety_buffer)
                        internal_polygons_2d.append(buffered_hole_poly)
                        print(f"    为内部轮廓 {internal_contour_data['id']} 创建了缓冲区为 {safety_buffer:.2f} 的孔洞多边形")
                except Exception as e:
                    print(f"    警告: 创建内部轮廓 {internal_contour_data['id']} 的2D多边形时出错: {e}")

        # 3. 生成直接偏置的填充路径
        offset_dir_axis = self.axis_index
        min_bound_offset_axis = self.mesh_bounds[0, offset_dir_axis]
        max_bound_offset_axis = self.mesh_bounds[1, offset_dir_axis]
        start_offset = min_bound_offset_axis + offset_from_bounds
        end_offset = max_bound_offset_axis - offset_from_bounds

        if start_offset >= end_offset:
            print(f"警告: 偏置范围无效 (start={start_offset:.3f}, end={end_offset:.3f})。无法生成填充。")
            return final_paths_list, []

        # 根据是否启用自适应密度，使用不同的切片位置生成策略
        if adaptive_density:
            # The old logic for choosing between ny_based and curvature_heuristic is now replaced by the iterative method.
            
            print(f"  启用自适应密度: 将使用迭代反馈方法。") 

            target_3d_spacing_objective = row_spacing 
            
            main_boundary_for_iterative_slicer = None
            if external_contours_3d: 
                if external_contours_3d[0]['points_3d'] is not None and len(external_contours_3d[0]['points_3d']) >= 2:
                    external_boundary_points_2d = external_contours_3d[0]['points_2d']
                    if len(external_boundary_points_2d) >= 2:
                        main_boundary_for_iterative_slicer = LineString(external_boundary_points_2d)
            
            if main_boundary_for_iterative_slicer is None or not main_boundary_for_iterative_slicer.is_valid:
                print(f"    错误 (_create_direct_offset_paths): 无法为迭代切片确定有效的外部边界LineString。将回退到固定间距。")
                num_steps = int(np.floor((end_offset - start_offset) / row_spacing)) + 1
                slice_positions = [start_offset + i * row_spacing for i in range(num_steps)] if num_steps > 0 else []
            else:
                print(f"    使用迭代方法确定切片位置。主边界: {main_boundary_for_iterative_slicer.geom_type} (长度: {main_boundary_for_iterative_slicer.length:.2f})")
                print(f"    迭代切片将使用 {len(internal_polygons_2d)} 个内部2D缓冲孔洞多边形。")

                slice_positions = self.generate_adaptive_slice_positions_iterative(
                    start_offset=start_offset, 
                    end_offset=end_offset,
                    target_3d_spacing_objective=target_3d_spacing_objective,
                    offset_dir_axis=offset_dir_axis,
                    boundary_shapely_linestring=main_boundary_for_iterative_slicer, 
                    inner_contours_shapely_list=internal_polygons_2d, 
                    proximity_threshold=proximity_threshold, 
                    max_segment_length=max_segment_length,
                    min_delta_y_factor=iter_min_delta_y_factor,
                    max_delta_y_factor=iter_max_delta_y_factor,
                    tolerance_abs=iter_tolerance_abs,
                    iter_max_iterations_per_step=iter_max_iterations_per_step,
                    iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc
                )
        else:
            # 使用传统的固定间距切片
            num_steps = int(np.floor((end_offset - start_offset) / row_spacing)) + 1
            if num_steps <= 0:
                print(f"警告: 计算的偏置步数为零或负 ({num_steps})。无法生成填充。")
                return final_paths_list, []
            slice_positions = [start_offset + i * row_spacing for i in range(num_steps)]
            print(f"  固定间距模式: 从 {start_offset:.3f} 到 {end_offset:.3f}, 共 {len(slice_positions)} 个切片位置。")
        
        if not slice_positions:
            print(f"警告: 未能生成任何切片位置。")
            return final_paths_list, []
            
        print(f"  偏置轴 ({'X' if offset_dir_axis == 0 else 'Y'}): 将处理 {len(slice_positions)} 个切片位置")

        all_strips_data = []
        for slice_idx, current_offset_val in enumerate(slice_positions):
            plane_normal = np.zeros(3)
            plane_normal[offset_dir_axis] = 1.0
            plane_origin_base = self.mesh.centroid.copy()
            plane_origin_base[offset_dir_axis] = current_offset_val
            plane_origin = plane_origin_base

            try:
                path3d_slice = self.mesh.section(plane_origin=plane_origin, plane_normal=plane_normal)
            except Exception as e:
                print(f"警告: 在偏置值 {current_offset_val:.3f} 进行切片时发生错误: {e}")
                path3d_slice = None
            
            polylines_in_slice = []
            if path3d_slice is not None and path3d_slice.discrete:
                polylines_in_slice.extend(path3d_slice.discrete)
            elif path3d_slice is not None and path3d_slice.vertices is not None and len(path3d_slice.vertices) >= self.min_points_req:
                raw_vertices_from_slice = path3d_slice.vertices
                sort_key_axis_for_slice = 1 - offset_dir_axis
                # Trimesh的section有时返回未排序的点，或多个不相连的段落作为一个列表。
                # 这是一个简化处理：假设排序能处理单个连续段。更复杂的断开检测可能需要。
                # TODO: 如果section返回多个不连续段，这里的排序和处理逻辑可能需要改进。
                # 例如，可以通过距离阈值将raw_vertices_from_slice分割成多个polylines_in_slice。
                # 目前，按扫描轴排序，并希望它形成一个（或多个可被后续逻辑处理的）合理顺序。
                indices = np.argsort(raw_vertices_from_slice[:, sort_key_axis_for_slice])
                sorted_vertices = raw_vertices_from_slice[indices]
                polylines_in_slice.append(sorted_vertices) # 将排序后的作为一个polyline处理

            for polyline_vertices_initial in polylines_in_slice:
                if len(polyline_vertices_initial) < self.min_points_req:
                    continue

                # --- Start: Original Trimming Logic (d_surf/2) ---
                # This part is complex. It sorts (already done for .vertices case), then trims.
                # For .discrete case, polyline_vertices_initial is one segment.
                # For .vertices case, it's the sorted list.
                
                current_strip_points_to_process_trim = polyline_vertices_initial # Points before d_surf/2 trim
                
                trim_length = self.d_surf / 2.0 
                points_after_dsurf_trim = current_strip_points_to_process_trim # Default if no trim
                
                if len(current_strip_points_to_process_trim) >= self.min_points_req and trim_length > 1e-6:
                    points_for_trimming_orig = current_strip_points_to_process_trim
                    segment_lengths_orig = np.linalg.norm(np.diff(points_for_trimming_orig, axis=0), axis=1)
                    cumulative_lengths_orig = np.concatenate(([0], np.cumsum(segment_lengths_orig)))
                    total_path_length_orig = cumulative_lengths_orig[-1]

                    processed_points_list_temp = list(points_for_trimming_orig)

                    # Trim start
                    if total_path_length_orig > trim_length:
                        new_start_found = False
                        for i_trim in range(len(cumulative_lengths_orig)):
                            if cumulative_lengths_orig[i_trim] >= trim_length:
                                if i_trim > 0:
                                    p_b, p_a = points_for_trimming_orig[i_trim-1], points_for_trimming_orig[i_trim]
                                    l_b, l_a = cumulative_lengths_orig[i_trim-1], cumulative_lengths_orig[i_trim]
                                    seg_l = l_a - l_b
                                    if seg_l > 1e-9:
                                        t = (trim_length - l_b) / seg_l
                                        t = np.clip(t, 0.0, 1.0)
                                        new_start_pt = p_b + t * (p_a - p_b)
                                        processed_points_list_temp = [new_start_pt] + processed_points_list_temp[i_trim:]
                                        new_start_found = True
                                break
                        if not new_start_found and cumulative_lengths_orig[-1] < trim_length: pass # Path too short

                    # Recalculate for end trim if start was trimmed
                    points_after_start_trim_temp = np.array(processed_points_list_temp)
                    if len(points_after_start_trim_temp) >= 2:
                        segment_lengths_orig = np.linalg.norm(np.diff(points_after_start_trim_temp, axis=0), axis=1)
                        cumulative_lengths_orig = np.concatenate(([0], np.cumsum(segment_lengths_orig)))
                        total_path_length_orig = cumulative_lengths_orig[-1]
                    else:
                        total_path_length_orig = 0
                    
                    # Trim end
                    if total_path_length_orig > trim_length:
                        new_end_found = False
                        current_pts_for_end_trim = np.array(processed_points_list_temp) # Use updated list
                        for i_trim in range(len(cumulative_lengths_orig) - 1, -1, -1):
                            len_from_end = total_path_length_orig - cumulative_lengths_orig[i_trim]
                            if len_from_end >= trim_length:
                                if i_trim < len(cumulative_lengths_orig) - 1 :
                                    p_b_end, p_a_end = current_pts_for_end_trim[i_trim], current_pts_for_end_trim[i_trim+1]
                                    # len_from_end_at_pb = total_path_length_orig - cumulative_lengths_orig[i_trim]
                                    # len_from_end_at_pa = total_path_length_orig - cumulative_lengths_orig[i_trim+1]
                                    # seg_l_end = len_from_end_at_pb - len_from_end_at_pa
                                    seg_l_end_actual = np.linalg.norm(p_a_end - p_b_end)

                                    if seg_l_end_actual > 1e-9:
                                        # t is from p_b_end towards p_a_end
                                        # We want dist(new_end_pt, original_end_pt) == trim_length
                                        # dist(p_b_end, original_end_pt) = len_from_end_at_pb
                                        # Need t such that dist from p_b_end + t*(p_a_end - p_b_end) to original_end_pt is trim_length
                                        # This implies moving (len_from_end_at_pb - trim_length) along segment p_b_end -> p_a_end
                                        t = ( (total_path_length_orig - cumulative_lengths_orig[i_trim]) - trim_length) / seg_l_end_actual
                                        t = np.clip(t, 0.0, 1.0)
                                        new_end_pt = p_b_end + t * (p_a_end - p_b_end)
                                        processed_points_list_temp = processed_points_list_temp[:i_trim+1] + [new_end_pt]
                                        new_end_found = True
                                break 
                        if not new_end_found and total_path_length_orig < trim_length: pass

                    points_after_dsurf_trim = np.array(processed_points_list_temp)
                # --- End: Original Trimming Logic ---

                if len(points_after_dsurf_trim) < self.min_points_req:
                    continue
                
                # Interpolate the (potentially d_surf/2 trimmed) strip
                strip_candidate_points = self.interpolate_path_points(points_after_dsurf_trim, max_segment_length)
                if len(strip_candidate_points) < self.min_points_req:
                    continue
                
                # 使用批量法线计算优化性能
                strip_candidate_normals = self.get_surface_normals_batch(strip_candidate_points)

                # --- NEW FILL SEGMENT IDENTIFICATION LOGIC using helper method ---
                # Call the new helper method to get fill segments
                fill_sub_segments = self._segment_strip_by_3d_intersections(
                    strip_candidate_points, 
                    strip_candidate_normals, 
                    all_3d_boundary_linestrings
                )

                if fill_sub_segments:
                    for sub_segment_data in fill_sub_segments:
                        current_sub_points = sub_segment_data['points']
                        current_sub_normals = sub_segment_data['normals'] # Original normals for the sub-segment

                        # Trim both ends of this fill sub-segment
                        trimmed_sub_points = self._trim_path_ends(current_sub_points, self.d_surf / 2.0)

                        if trimmed_sub_points is not None and len(trimmed_sub_points) >= self.min_points_req:
                            # Normals need to be re-evaluated for the trimmed path
                            trimmed_sub_normals = self.get_surface_normals_batch(trimmed_sub_points)
                            
                            if trimmed_sub_normals is not None and len(trimmed_sub_normals) == len(trimmed_sub_points):
                                all_strips_data.append({
                                    'offset_val': current_offset_val,
                                    'points': trimmed_sub_points,
                                    'normals': trimmed_sub_normals,
                                    'slice_dir_axis_start_coord': trimmed_sub_points[0, 1 - offset_dir_axis]
                                })
                            # else:
                                # print(f"      警告: 修剪后的子段未能获取有效法线，跳过。点数: {len(trimmed_sub_points)}") # 可选
                        # else:
                            # print(f"      警告: 修剪后的子段点数不足 ({len(trimmed_sub_points) if trimmed_sub_points is not None else 'None'}) 或为None，跳过。原始点数: {len(current_sub_points)}") # 可选
                # --- END NEW FILL SEGMENT LOGIC (replaced old proximity filtering and hole avoidance) ---

            # 此处原有的segments_avoiding_holes 和 proximity filtering 逻辑已被新的基于交点的分段逻辑取代
            # 因此，那些代码块应该已经被移除，或者现在这个新的逻辑是唯一处理 strip_candidate_points 的地方

        if not all_strips_data:
            print("直接偏置策略：未能从截面生成任何路径条带（在修剪、插值和边界过滤后）。")
            return final_paths_list, []
            
        all_strips_data.sort(key=lambda s: (s['offset_val'], s['slice_dir_axis_start_coord']))
        print(f"  直接偏置策略：共生成 {len(all_strips_data)} 个过滤后的3D路径条带/段。现在尝试蛇形连接...")
        
        # --- Calculate and Print Actual 3D Spacing between Offset Layers ---
        if all_strips_data:
            strips_by_offset_val = {}
            for strip_data_item in all_strips_data: # Renamed to avoid conflict with outer scope
                offset_val = strip_data_item['offset_val']
                if offset_val not in strips_by_offset_val:
                    strips_by_offset_val[offset_val] = []
                # Ensure 'points' key exists and is a numpy array with at least 1 point
                if 'points' in strip_data_item and isinstance(strip_data_item['points'], np.ndarray) and strip_data_item['points'].ndim == 2 and strip_data_item['points'].shape[0] > 0:
                    strips_by_offset_val[offset_val].append(strip_data_item['points'])

            sorted_unique_offset_vals = sorted(strips_by_offset_val.keys())

            print(f"\n  --- 实际3D间距分析 (层间，蛇形连接前) ---") # 取消注释
            if len(sorted_unique_offset_vals) < 2:
                print("    只有一个偏置层或没有足够的偏置层来进行间距分析。") # 取消注释
                pass # 如果只有一个偏置层，则没有间距可分析，但保持结构
            
            offset_values_to_remove = set() # 用于存储需要移除的偏置值
            min_actual_3d_spacing_allowed = row_spacing / 2.0 # 最小允许的平均3D间距
            print(f"    最小允许平均3D间距阈值 (target_row_spacing/2): {min_actual_3d_spacing_allowed:.4f}mm") # 取消注释

            scan_axis = 1 - offset_dir_axis
            spacing_analysis_data = [] # 初始化用于存储间距分析数据的列表

            for k_offset_idx in range(len(sorted_unique_offset_vals) - 1): # Renamed loop variable
                offset_val_1 = sorted_unique_offset_vals[k_offset_idx]
                offset_val_2 = sorted_unique_offset_vals[k_offset_idx+1]

                strips_layer1 = strips_by_offset_val.get(offset_val_1, [])
                strips_layer2 = strips_by_offset_val.get(offset_val_2, [])

                if not strips_layer1 or not strips_layer2:
                    print(f"    跳过间距分析: ~{offset_val_1:.3f}mm 和 ~{offset_val_2:.3f}mm 之间的层数据不足。")
                    continue

                projected_step_dist = abs(offset_val_2 - offset_val_1)
                
                # 调用新的辅助函数来计算3D间距统计
                # 注意: num_samples_on_strip1 可以根据需要调整或作为参数传入
                spacing_stats = self._calculate_actual_3d_spacing_between_strip_sets(
                    strips_layer1, strips_layer2, scan_axis, num_samples_on_strip1=10
                )

                if spacing_stats and spacing_stats['num_samples'] > 0:
                    avg_dist = spacing_stats['avg_dist']
                    min_dist = spacing_stats['min_dist']
                    max_dist = spacing_stats['max_dist']
                    median_dist = spacing_stats['median_dist']
                    std_dev_dist = spacing_stats['std_dev_dist']
                    num_samples_calc = spacing_stats['num_samples']
                    
                    error_mm = avg_dist - target_3d_spacing_objective # 计算误差

                    spacing_analysis_data.append({
                        'Offset1_mm': offset_val_1,
                        'Offset2_mm': offset_val_2,
                        'ProjectedStep_mm': projected_step_dist,
                        'Target3DSpacing_mm': target_3d_spacing_objective, # 使用迭代方法的目标间距
                        'ActualAvg3DSpacing_mm': avg_dist,
                        'Error_mm': error_mm,
                        'Min3DSpacing_mm': min_dist,
                        'Max3DSpacing_mm': max_dist,
                        'Median3DSpacing_mm': median_dist,
                        'StdDev3DSpacing_mm': std_dev_dist,
                        'NumSamples': num_samples_calc
                    })

                    # print(f"  偏移层间距: ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm (投影步长: {projected_step_dist:.4f}mm)")
                    # print(f"    样本数: {num_samples_calc}")
                    # print(f"    3D间距 -> 平均: {avg_dist:.4f}mm | 最小: {min_dist:.4f}mm | 最大: {max_dist:.4f}mm | 中位数: {median_dist:.4f}mm | 标准差: {std_dev_dist:.4f}mm")

                    if avg_dist < min_actual_3d_spacing_allowed and not np.isnan(avg_dist):
                        print(f"    警告: 偏移层 ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm 间的平均3D间距 {avg_dist:.4f}mm 过小 (小于阈值 {min_actual_3d_spacing_allowed:.4f}mm)。")
                        print(f"    将标记与偏置值 {offset_val_2:.3f}mm 相关的所有路径条带进行移除。")
                        offset_values_to_remove.add(offset_val_2)
                    elif np.isnan(avg_dist):
                         print(f"    警告: 偏移层 ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm 间的平均3D间距无法计算 (NaN)。")
                else:
                    print(f"  偏移层间距: ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm (投影步长: {projected_step_dist:.4f}mm)")
                    print(f"    未能计算有效的3D间距 (样本数: {spacing_stats['num_samples'] if spacing_stats else 0}).")
                    spacing_analysis_data.append({
                        'Offset1_mm': offset_val_1,
                        'Offset2_mm': offset_val_2,
                        'ProjectedStep_mm': projected_step_dist,
                        'Target3DSpacing_mm': target_3d_spacing_objective,
                        'ActualAvg3DSpacing_mm': float('nan'),
                        'Error_mm': float('nan'),
                        'Min3DSpacing_mm': float('nan'),
                        'Max3DSpacing_mm': float('nan'),
                        'Median3DSpacing_mm': float('nan'),
                        'StdDev3DSpacing_mm': float('nan'),
                        'NumSamples': spacing_stats['num_samples'] if spacing_stats else 0
                    })
            
            print("  --- 实际3D间距分析结束 ---")

            if offset_values_to_remove:
                print(f"  正在根据3D间距过滤移除 {len(offset_values_to_remove)} 个偏置值的路径条带...")
                original_strip_count_before_filter = len(all_strips_data)
                all_strips_data = [strip for strip in all_strips_data if strip['offset_val'] not in offset_values_to_remove]
                print(f"  过滤完成。移除了 {original_strip_count_before_filter - len(all_strips_data)} 个路径条带。剩余 {len(all_strips_data)} 个。")
                if not all_strips_data:
                    print("警告: 过滤后没有剩余路径条带可用于蛇形连接。")
                    # 如果所有条带都被过滤掉，则提前返回，因为后续的蛇形连接会出错
                    # 确保返回的 final_paths_list 至少包含边界（如果之前已添加）
                    # 边界路径是在此函数开始时添加到 final_paths_list 的。
                    # 如果这里的 all_strips_data 为空，意味着没有填充路径。
                    # 蛇形连接逻辑本身应该能处理空的 all_strips_data，但最好明确。
                    # 此处直接让后续逻辑处理空的 all_strips_data。
        # --- End Calculate and Print Actual 3D Spacing ---

        # --- Path Strips Snake-like Connection ---
        t_start_snake_connection = time.time()
        max_connection_dist_factor = 2.5 
        connection_normal_similarity_threshold = 0.5 
        max_connection_length = max_connection_dist_factor * row_spacing
        
        current_long_path_points = [] 
        current_long_path_normals = []
        direction_for_next_strip = True 
        
        try:
            for strip_idx, current_strip_data in enumerate(all_strips_data):
                strip_pts_orig = current_strip_data['points']
                strip_nmls_orig = current_strip_data['normals']

                processed_strip_pts = np.copy(strip_pts_orig) 
                processed_strip_nmls = np.copy(strip_nmls_orig)

                if not direction_for_next_strip: 
                    processed_strip_pts = processed_strip_pts[::-1] 
                    processed_strip_nmls = processed_strip_nmls[::-1] 
                
                if not current_long_path_points: 
                    current_long_path_points.extend(list(processed_strip_pts))
                    current_long_path_normals.extend(list(processed_strip_nmls))
                else: 
                    prev_strip_end_point = np.array(current_long_path_points[-1])
                    prev_strip_end_normal = np.array(current_long_path_normals[-1])
                    next_strip_start_point = processed_strip_pts[0]
                    next_strip_start_normal = processed_strip_nmls[0]

                    connection_possible = True 
                    connection_vector = next_strip_start_point - prev_strip_end_point 
                    connection_distance = np.linalg.norm(connection_vector)

                    if connection_distance > max_connection_length: 
                        connection_possible = False
                    
                    if connection_possible: 
                        norm_prev_mag = np.linalg.norm(prev_strip_end_normal)
                        norm_next_mag = np.linalg.norm(next_strip_start_normal)
                        if norm_prev_mag > 1e-6 and norm_next_mag > 1e-6: 
                            norm_prev_unit = prev_strip_end_normal / norm_prev_mag
                            norm_next_unit = next_strip_start_normal / norm_next_mag
                            dot_product = np.dot(norm_prev_unit, norm_next_unit) 
                            if dot_product < connection_normal_similarity_threshold: 
                                connection_possible = False
                        else: 
                            connection_possible = False 
                    
                    if connection_possible and connection_distance > 1e-6: 
                        ray_origin_offset = 1e-4 
                        ray_length_reduction = 2e-4 
                        ray_dir_normalized = np.array([0.0,0.0,0.0]) 
                        if connection_distance > 1e-9: 
                             ray_dir_normalized = connection_vector / connection_distance

                        actual_ray_origin = prev_strip_end_point + ray_dir_normalized * ray_origin_offset
                        actual_max_ray_distance = connection_distance - ray_length_reduction 
                        
                        if actual_max_ray_distance > 1e-6: 
                            locations, _, _ = self.mesh.ray.intersects_location(
                                ray_origins=[actual_ray_origin], ray_directions=[ray_dir_normalized]
                            )
                            if len(locations) > 0: 
                                closest_hit_distance = np.linalg.norm(locations[0] - actual_ray_origin)
                                if closest_hit_distance < actual_max_ray_distance: 
                                    connection_possible = False 
                        
                    if connection_possible: 
                        current_long_path_points.extend(list(processed_strip_pts)) 
                        current_long_path_normals.extend(list(processed_strip_nmls))
                    else: 
                        if len(current_long_path_points) >= self.min_points_req:
                            final_paths_list.append((np.array(current_long_path_points), 
                                                     np.array(current_long_path_normals), 
                                                     False, fill_segment_id_counter))
                            fill_segment_id_counter -=1
                        
                        current_long_path_points = list(processed_strip_pts) 
                        current_long_path_normals = list(processed_strip_nmls)
                
                direction_for_next_strip = not direction_for_next_strip

            # Add the last accumulated long path
            if current_long_path_points and len(current_long_path_points) >= self.min_points_req:
                final_paths_list.append((np.array(current_long_path_points), 
                                         np.array(current_long_path_normals), 
                                         False, fill_segment_id_counter))
        except Exception as e_snake: # Correctly indented except block
            print(f"!!!!!!!!!!!!!!!!! 错误: 在蛇形连接逻辑中发生异常 !!!!!!!!!!!!!!!!!!")
            print(f"错误类型: {type(e_snake)}")
            print(f"错误信息: {e_snake}")
            import traceback
            print("Traceback:")
            traceback.print_exc()
            print(f"发生异常时，current_long_path_points 中有 {len(current_long_path_points)} 个点。")
            if 'strip_idx' in locals(): # Check if strip_idx is defined
                 print(f"异常可能发生在处理 strip_idx = {strip_idx} 时。")

        # print(f"--- 直接偏置策略 蛇形连接部分耗时: {time.time() - t_start_snake_connection:.4f} 秒 ---")
        num_fill_paths_added = sum(1 for _, _, is_b, _ in final_paths_list if not is_b)
        print(f"  直接偏置策略：蛇形连接后共添加 {num_fill_paths_added} 个填充路径段。")
        return final_paths_list, spacing_analysis_data

    def save_paths_to_gcode(self, paths_data, output_gcode_file, 
                            klipper_mode=False, 
                            feed_rate_print=600, feed_rate_travel=3000, 
                            target_extruder_temp=210, target_bed_temp=60,
                            filament_diameter=1.75, extrusion_width=0.4, layer_height=0.2, 
                            retraction_amount=1.0, retraction_feedrate=2700, 
                            extrusion_multiplier=1.0,
                            normal_hop_distance=1.0, 
                            clearance_above_model_max=5.0, 
                            rotation_feed_rate=3000, 
                            enable_rotation_axes=True # 是否启用ABC旋转轴输出
                            ):
        """将路径数据保存为G-code文件，采用高级抬刀逻辑（抬至模型最大Z之上），并将XYZ原点调整。"""
        if not paths_data:
            print("警告: save_paths_to_gcode 没有路径数据可写。")
            return

        # 根据模式设置G-code注释
        mode_description = "6-Axis G-code Mode (with ABC rotation axes)" if enable_rotation_axes else "Traditional G-code Mode (XYZ coordinates only)"

        # 计算所有路径点的整体边界，用于确定偏移量和安全Z高度
        min_x_overall, min_y_overall, min_z_overall = float('inf'), float('inf'), float('inf')
        max_x_overall, max_y_overall, max_z_overall = -float('inf'), -float('inf'), -float('inf') 
        has_any_points = False # 标记是否有任何有效点

        for points_segment, _, _, _ in paths_data:
            if points_segment is not None and len(points_segment) > 0:
                has_any_points = True
                current_min_x, current_min_y, current_min_z = np.min(points_segment[:, 0]), np.min(points_segment[:, 1]), np.min(points_segment[:, 2])
                current_max_x, current_max_y, current_max_z = np.max(points_segment[:, 0]), np.max(points_segment[:, 1]), np.max(points_segment[:, 2]) 
                min_x_overall = min(min_x_overall, current_min_x)
                min_y_overall = min(min_y_overall, current_min_y)
                min_z_overall = min(min_z_overall, current_min_z)
                max_x_overall = max(max_x_overall, current_max_x)
                max_y_overall = max(max_y_overall, current_max_y)
                max_z_overall = max(max_z_overall, current_max_z)
        
        x_offset, y_offset, z_offset = 0.0, 0.0, 0.0 # 初始化XYZ偏移
        absolute_safe_z_level_gcode = 20.0 # 默认的绝对安全Z高度 (G-code坐标系)

        if has_any_points:
            center_x, center_y = (min_x_overall + max_x_overall) / 2.0, (min_y_overall + max_y_overall) / 2.0
            x_offset = 98.5 - center_x 
            y_offset = 100.0 - center_y 
            z_offset = 0.0 # Original Z values are maintained by not applying a Z offset
            absolute_safe_z_level_gcode = max_z_overall + z_offset + clearance_above_model_max # z_offset is 0.0 here
        else:
            print("警告: G-code: 未找到有效点确定偏移。无XYZ偏移输出，安全Z高度将使用默认值。")

        with open(output_gcode_file, 'w', encoding='utf-8') as f:
            f.write(f"; G-code Generated by DirectProjectionSlicer (Advanced Hop Logic)\n")
            f.write(f"; Mode: {mode_description}\n")
            f.write(f"; Original Mesh: {self.mesh_path}\n")
            f.write(f"; XY offset to center model approx at (100,100). Original Z values are maintained.\\n")
            f.write(f"; Applied X_offset: {x_offset:.4f}, Y_offset: {y_offset:.4f}, Z_offset: {z_offset:.4f}\n")
            f.write(f"; Normal Hop Distance: {normal_hop_distance:.2f}mm, Max Clearance Above Model: {clearance_above_model_max:.2f}mm (Target Z for hop: {absolute_safe_z_level_gcode:.4f})\n")
            
            # 添加打印参数信息（无论何种模式都显示）
            f.write(f"; Filament Diameter: {filament_diameter:.2f}mm, Extrusion Width: {extrusion_width:.2f}mm, Layer Height: {layer_height:.2f}mm\n")
            f.write(f"; Retraction: {retraction_amount:.2f}mm @ {retraction_feedrate}mm/min\n")
            f.write(f"; Extrusion Multiplier: {extrusion_multiplier*100:.1f}%\n")
            
            if klipper_mode:
                f.write(f"; Klipper Mode Activated\n")

            
            f.write(f"G21 ; Set units to millimeters\n")
            f.write(f"G90 ; Use absolute XYZ positioning\n")
            f.write(f"M83 ; Use relative E distances (safer for Klipper extrusion calculation with SET_EXTRUDER_ROTATION_DISTANCE)\n" if klipper_mode else "M82 ; Use absolute E distances\n")
            
            if klipper_mode:
                f.write(f"PRINT_START BED_TEMP={target_bed_temp} EXTRUDER_TEMP={target_extruder_temp}\n")
            else: 
                f.write(f"M104 S{target_extruder_temp} ; Set extruder temperature (no wait)\n")
                f.write(f"M140 S{target_bed_temp} ; Set bed temperature (no wait)\n")
                f.write(f"M109 S{target_extruder_temp} ; Wait for extruder to reach temperature\n")
                f.write(f"M190 S{target_bed_temp} ; Wait for bed to reach temperature\n")

            initial_safe_z = absolute_safe_z_level_gcode + 5.0 

            current_x, current_y, current_z = 0, 0, initial_safe_z 
            current_a, current_b, current_c = 0.0, 0.0, 0.0 
            current_e = 0.0 
            
            if enable_rotation_axes:
                f.write(f"G0 X{current_x:.4f} Y{current_y:.4f} Z{current_z:.4f} A{current_a:.3f} B{current_b:.3f} C{current_c:.3f} F{feed_rate_travel} ; Initial position and orientation\n")
            else:
                f.write(f"G0 X{current_x:.4f} Y{current_y:.4f} Z{current_z:.4f} F{feed_rate_travel} ; Initial position (Traditional mode)\n")
            f.write(f"; -- Path Data Start --\n\n")
            
            filament_area = math.pi * (filament_diameter / 2.0)**2
            extrusion_per_mm = 0 
            if filament_area > 0: 
                extrusion_per_mm = (extrusion_width * layer_height) / filament_area

            last_normal_for_hop = None 

            for path_idx, (original_points_segment, normals_segment, is_boundary, segment_id) in enumerate(paths_data):
                if original_points_segment is None or len(original_points_segment) < 1 or \
                   (normals_segment is None or len(normals_segment) != len(original_points_segment)): 
                    print(f"  G-code: Skipping invalid or normal-mismatched path segment (ID: {segment_id})")
                    continue
                
                current_segment_points_gcode = np.copy(original_points_segment)
                current_segment_points_gcode[:, 0] += x_offset
                current_segment_points_gcode[:, 1] += y_offset
                current_segment_points_gcode[:, 2] += z_offset

                path_type_str = "BOUNDARY" if is_boundary else f"FILL_SEGMENT_{segment_id}"
                f.write(f"; ---- Preparing Path Segment {path_idx + 1}, OriginalID: {segment_id}, Type: {path_type_str}, Points: {len(current_segment_points_gcode)} ----\n")
                
                target_print_start_coord = current_segment_points_gcode[0] 
                target_print_start_normal_raw = normals_segment[0] 
                pre_approach_point_calculated = target_print_start_coord 
                if normal_hop_distance > 0:
                    norm_val_next_start = np.linalg.norm(target_print_start_normal_raw)
                    if norm_val_next_start > 1e-6:
                        normalized_target_start_normal = target_print_start_normal_raw / norm_val_next_start
                        tool_axis_for_approach = -normalized_target_start_normal if self.inward_normals else normalized_target_start_normal
                        offset_vector_for_pre_approach = tool_axis_for_approach * normal_hop_distance
                        pre_approach_point_calculated = target_print_start_coord + offset_vector_for_pre_approach
                    else:
                        f.write(f"; WARNING: Start normal for segment {segment_id} is zero vector. Will use direct approach for reorientation.\n")
                
                # 2. In current safe Z height move to XY of pre-approach point
                if not (np.allclose(current_x, pre_approach_point_calculated[0]) and np.allclose(current_y, pre_approach_point_calculated[1])):
                    # Remove E parameter for moving to pre-approach XY
                    f.write(f"G0 X{pre_approach_point_calculated[0]:.4f} Y{pre_approach_point_calculated[1]:.4f} Z{current_z:.4f} F{feed_rate_travel} ; Move to Pre-Approach XY (Safe Z)\n") # Z remains current, only XY moves
                    current_x, current_y = pre_approach_point_calculated[0], pre_approach_point_calculated[1]

                # 3. Lower to Z height of pre-approach point
                if not np.allclose(current_z, pre_approach_point_calculated[2]):
                    # Remove E parameter for lowering to pre-approach Z
                    f.write(f"G0 X{current_x:.4f} Y{current_y:.4f} Z{pre_approach_point_calculated[2]:.4f} F{feed_rate_travel} ; Lower to Pre-Approach Z\n") # XY remains current, only Z moves
                    current_z = pre_approach_point_calculated[2]
                
                # 4. Adjust tool orientation at pre-approach point
                try:
                    target_start_a, target_start_b, target_start_c = self.normal_to_rpy_degrees(target_print_start_normal_raw)
                except Exception as e_reorient:
                    print(f"  G-code: Error calculating RPY for segment {segment_id} start: {e_reorient}. Using A0 B0 C0.\n")
                    target_start_a, target_start_b, target_start_c = 0.0, 0.0, 0.0 # Use default orientation on error
                
                if not (np.allclose(current_a, target_start_a) and np.allclose(current_b, target_start_b) and np.allclose(current_c, target_start_c)):
                    # Remove E parameter for adjusting orientation
                    if enable_rotation_axes:
                        f.write(f"G0 A{target_start_a:.3f} B{target_start_b:.3f} C{target_start_c:.3f} F{rotation_feed_rate} ; Reorient at Pre-Approach Point\n")
                    else:
                        f.write(f"G0 F{rotation_feed_rate} ; Reorient at Pre-Approach Point (Traditional mode)\n")
                    current_a, current_b, current_c = target_start_a, target_start_b, target_start_c
                
                # 5. (Optional) Prime/Unretract
                if klipper_mode and retraction_amount > 0: # Klipper uses M83 (relative extrusion)
                    f.write(f"G1 E{retraction_amount:.5f} F{retraction_feedrate} ; Prime (Klipper M83)\n")
                elif not klipper_mode and retraction_amount > 0: # Standard firmware uses M82 (absolute extrusion)
                     current_e += retraction_amount # Increase extrusion amount
                     f.write(f"G1 E{current_e:.5f} F{retraction_feedrate} ; Prime (M82)\n")

                # --- Start printing current path segment ---
                f.write(f"; Printing Path Segment {path_idx + 1}\n")
                for i in range(len(current_segment_points_gcode)):
                    p_coord = current_segment_points_gcode[i] # Current point coordinates (already offset)
                    p_normal = normals_segment[i] # Current point normal (original)
                    try:
                        roll_deg, pitch_deg, yaw_deg = self.normal_to_rpy_degrees(p_normal)
                    except Exception as e_rpy:
                        print(f"  G-code: Error calculating normal RPY for point {i} (Segment ID {segment_id}): {e_rpy}. Using previous angles.\n")
                        roll_deg, pitch_deg, yaw_deg = current_a, current_b, current_c # Maintain previous orientation on error

                    gcode_line_parts = [f"G1 X{p_coord[0]:.4f} Y{p_coord[1]:.4f} Z{p_coord[2]:.4f}"]
                    
                    # --- Calculate Extrusion ---
                    if i == 0: # First point (landing move), no extrusion
                        pass # No extrusion operation
                    else: # Subsequent points (i > 0), calculate and add extrusion
                        if klipper_mode: # Klipper (M83 relative extrusion)
                            prev_print_point = current_segment_points_gcode[i-1]
                            segment_length = math.sqrt(
                                (p_coord[0] - prev_print_point[0])**2 +
                                (p_coord[1] - prev_print_point[1])**2 +
                                (p_coord[2] - prev_print_point[2])**2
                            )
                            delta_e = 0.0
                            if segment_length > 1e-9:
                                # 简化：由于slope_compensation_exponent=0，补偿因子总是1.0
                                delta_e = segment_length * extrusion_per_mm * extrusion_multiplier
                            
                            if delta_e > 1e-9: # Only add E parameter if extrusion amount is significant
                                gcode_line_parts.append(f"E{delta_e:.5f}")
                        
                        else: # Standard firmware (M82 absolute extrusion)
                            prev_print_point = current_segment_points_gcode[i-1]
                            segment_length = math.sqrt(np.sum((p_coord - prev_print_point)**2))
                            if segment_length > 1e-9: 
                               current_e += segment_length * extrusion_per_mm * extrusion_multiplier 
                            gcode_line_parts.append(f"E{current_e:.5f}")

                    if enable_rotation_axes:
                        gcode_line_parts.append(f"A{roll_deg:.3f} B{pitch_deg:.3f} C{yaw_deg:.3f}") # Add orientation
                    # 传统模式下不添加ABC角度，保持代码整洁
                    gcode_line_parts.append(f"F{feed_rate_print}") # Add print speed
                    f.write(" ".join(gcode_line_parts) + "\n")

                    # Update current state
                    current_x, current_y, current_z = p_coord[0], p_coord[1], p_coord[2]
                    current_a, current_b, current_c = roll_deg, pitch_deg, yaw_deg
                    last_normal_for_hop = p_normal # Save current normal for next hop
                
                # --- Advanced Hop after finishing current path segment ---
                f.write(f"; Path Segment {path_idx + 1} Print End\n")
                f.write(f"; Starting Advanced Hop from end of Segment {segment_id}\n")
                # 1. Retract
                if klipper_mode and retraction_amount > 0:
                    f.write(f"G1 E{-retraction_amount:.5f} F{retraction_feedrate} ; Retract before hop (Klipper M83)\n")
                elif not klipper_mode and retraction_amount > 0: 
                    current_e -= retraction_amount # Decrease extrusion amount
                    f.write(f"G1 E{current_e:.5f} F{retraction_feedrate} ; Retract before hop (M82)\n")

                # 2. Hop along tool axis (normal direction hop)
                hop_exit_point = np.array([current_x, current_y, current_z]) # Record point after normal hop
                if normal_hop_distance > 0 and last_normal_for_hop is not None:
                    norm_val = np.linalg.norm(last_normal_for_hop)
                    if norm_val > 1e-6:
                        # Tool axis direction for hop: if normals inward, tool axis is -normal; else normal.
                        tool_axis_direction_for_hop = -last_normal_for_hop if self.inward_normals else last_normal_for_hop
                        tool_axis_unit = tool_axis_direction_for_hop / norm_val
                        hop_dx, hop_dy, hop_dz = tool_axis_unit * normal_hop_distance
                        target_x_norm_hop, target_y_norm_hop, target_z_norm_hop = current_x + hop_dx, current_y + hop_dy, current_z + hop_dz
                        # Remove E parameter for tool axis normal hop
                        if enable_rotation_axes:
                            f.write(f"G1 X{target_x_norm_hop:.4f} Y{target_y_norm_hop:.4f} Z{target_z_norm_hop:.4f} A{current_a:.3f} B{current_b:.3f} C{current_c:.3f} F{feed_rate_travel} ; Hop along tool axis normal\n")
                        else:
                            f.write(f"G1 X{target_x_norm_hop:.4f} Y{target_y_norm_hop:.4f} Z{target_z_norm_hop:.4f} F{feed_rate_travel} ; Hop along tool axis normal (Traditional mode)\n")
                        current_x, current_y, current_z = target_x_norm_hop, target_y_norm_hop, target_z_norm_hop
                        hop_exit_point = np.array([current_x, current_y, current_z])
                    else:
                        f.write(f"; WARNING: Normal for hop is zero vector, skipping normal direction hop.\n")
                
                # 3. Return tool orientation to vertical (A0 B0)
                if not (np.allclose(current_a, 0) and np.allclose(current_b, 0)): # C-axis (Yaw) usually maintained or path-dependent
                    # Remove E parameter for returning tool to vertical
                    if enable_rotation_axes:
                        f.write(f"G0 A0.000 B0.000 C{current_c:.3f} F{rotation_feed_rate} ; Return tool to vertical (A, B axes, maintain C)\n") # Maintain C-axis
                    else:
                        f.write(f"G0 F{rotation_feed_rate} ; Return tool to vertical (Traditional mode)\n")
                    current_a, current_b = 0.0, 0.0
                
                # 4. Z-axis hop to absolute safe height
                if not np.allclose(current_z, absolute_safe_z_level_gcode):
                    # Remove E parameter for Z-axis secondary hop to absolute safe height
                    f.write(f"G0 Z{absolute_safe_z_level_gcode:.4f} F{feed_rate_travel} ; Z-axis secondary hop to absolute safe height\n")
                    current_z = absolute_safe_z_level_gcode
                
                f.write(f"; Advanced hop sequence for segment {segment_id} complete. Preparing approach for next segment.\n\n")
            
            f.write(f"; -- Path Data End --\n")
            if klipper_mode:
                if retraction_amount > 0 and has_any_points: # If content was printed, perform final retraction
                     f.write(f"G1 E{-retraction_amount:.5f} F{retraction_feedrate} ; Final Retraction (Klipper M83)\n")
                f.write(f"PRINT_END ; Call Klipper PRINT_END macro\n")
            else: # Standard firmware end sequence
                if retraction_amount > 0 and has_any_points: 
                     current_e -= retraction_amount
                     f.write(f"G1 E{current_e:.5f} F{retraction_feedrate} ; Final Retraction\n")
                f.write(f"M104 S0 ; Turn off extruder heater\n")
                f.write(f"M140 S0 ; Turn off bed heater\n")
                f.write(f"G91 ; Relative positioning\n")
                f.write(f"G1 Z10 F3000 ; Raise Z axis by 10mm\n")
                f.write(f"G90 ; Absolute positioning\n")
                f.write(f"G28 X0 Y0 ; Home XY axes\n")
                f.write(f"M84 ; Disable stepper motors\n")
                f.write(f"M2 ; Program End\n")

        print(f"G-code saved to {output_gcode_file}")

    def get_projected_boundary_contours(self, boundary_path_id_start=1):
        """
        获取所有边界路径，将其投影到XOY平面，并返回路径数据。
        返回:
        list_of_paths_data: 每个元素是字典 {'points_2d':二维点数组, 'points_3d':三维点数组, 'is_boundary':布尔值, 'id':路径ID}
        """
        raw_boundary_edges = self.get_boundary_edges()
        if not raw_boundary_edges:
            print("未找到边界边用于投影轮廓。")
            return []
        
        sorted_edge_paths_tuples = self.sort_boundary_edges(raw_boundary_edges)
        if not sorted_edge_paths_tuples:
            print("排序后未找到连续的边界路径用于投影。")
            return []

        projected_contours_data = []
        current_boundary_id = boundary_path_id_start
        for single_path_edges_tuples in sorted_edge_paths_tuples:
            if not single_path_edges_tuples: continue
            ordered_vertices_indices = []
            if len(single_path_edges_tuples) == 1: # 单一边构成路径
                v1, v2 = single_path_edges_tuples[0]
                ordered_vertices_indices.extend([v1, v2])
            else:
                # 从边重建顶点顺序
                # 这个简化逻辑假设 sort_boundary_edges 返回的边在某种程度上是有序的
                path_verts_temp = [single_path_edges_tuples[0][0], single_path_edges_tuples[0][1]]
                for i_edge in range(1, len(single_path_edges_tuples)):
                    e_v1, e_v2 = single_path_edges_tuples[i_edge]
                    if path_verts_temp[-1] == e_v1:
                        path_verts_temp.append(e_v2)
                    elif path_verts_temp[-1] == e_v2:
                        path_verts_temp.append(e_v1)
                    elif path_verts_temp[0] == e_v1: # 路径可能需要从另一端开始构建
                         path_verts_temp.insert(0, e_v2)
                    elif path_verts_temp[0] == e_v2:
                         path_verts_temp.insert(0, e_v1)
                    else: # 简单追加/前插无法处理的不连续或复杂连接
                        print(f"警告: 边界路径 {current_boundary_id} 顶点连接复杂，可能不完整。边: ({e_v1}, {e_v2}), 当前路径尾点: {path_verts_temp[-1]}")
                        # 后备方案: 如果是孤立的，只添加其中一个点，或者可以尝试更复杂的图遍历
                        # 目前，我们假设 sort_boundary_edges 提供的是基本首尾相连的段落
                        path_verts_temp.append(e_v1) # 任意选择
                        path_verts_temp.append(e_v2)

                ordered_vertices_indices = path_verts_temp
            
            # 去除连续重复的顶点索引
            unique_ordered_vertices = [ordered_vertices_indices[0]]
            for k_idx in range(1, len(ordered_vertices_indices)):
                if ordered_vertices_indices[k_idx] != ordered_vertices_indices[k_idx-1]:
                    unique_ordered_vertices.append(ordered_vertices_indices[k_idx])

            if len(unique_ordered_vertices) < self.min_points_req:
                print(f"边界路径 {current_boundary_id} 点数不足 ({len(unique_ordered_vertices)})，跳过投影。")
                continue
            
            boundary_points_3d = self.mesh.vertices[unique_ordered_vertices]
            projected_points_2d = boundary_points_3d[:, :2] # 投影到XOY平面
            
            projected_contours_data.append({
                'points_2d': projected_points_2d, 
                'points_3d': boundary_points_3d, 
                'is_boundary': True, 
                'id': current_boundary_id
            })
            print(f"已投影边界路径 {current_boundary_id}，包含 {len(projected_points_2d)} 个二维点。")
            current_boundary_id += 1
            
        return projected_contours_data

    def create_projected_fill_paths(self, row_spacing, 
                                    offset_distance=None, 
                                    max_segment_length=None, 
                                    strategy='direct_offset', # 路径生成策略
                                    projection_fill_pattern='raster', # 新增参数，用于投影策略
                                    proximity_threshold=0.15, # 与边界和孔洞的最小距离 (毫米)
                                    adaptive_density=True, # 自适应密度控制 (仅适用于direct_offset策略)
                                    # Removed old adaptive parameters like curvature_sensitivity, min_spacing_factor, max_spacing_factor for direct_offset
                                    # These are now handled by the iterative parameters if adaptive_density is on for direct_offset
                                    iter_min_delta_y_factor=0.05,
                                    iter_max_delta_y_factor=2.0,
                                    iter_tolerance_abs=0.1,
                                    iter_max_iterations_per_step=15,
                                    iter_num_samples_for_spacing_calc=7
                                    ):
        """ 
        主流程：根据选择的策略生成3D打印路径。
        
        strategy: 路径生成策略，当前支持 'direct_offset' 和 'projection'。
        projection_fill_pattern: 投影策略的2D填充模式 ('raster' 或 'concentric')。
        proximity_threshold: (对于direct_offset) 主要影响迭代切片器中孔洞的处理。
                           (对于projection) 当前未使用。
        adaptive_density: (仅用于direct_offset) 是否启用通过迭代方法实现的自适应密度控制。
        iter_min_delta_y_factor, iter_max_delta_y_factor, iter_tolerance_abs, 
        iter_max_iterations_per_step, iter_num_samples_for_spacing_calc: 
            (仅用于direct_offset且adaptive_density=True) 控制迭代自适应切片行为的参数。
        
        返回:
        final_paths_list: 包含所有路径段的列表 [(points, normals, is_boundary, segment_id), ...]
        spacing_data: (仅用于direct_offset) 包含间距分析数据的列表。
        """
        print(f"\n--- 开始路径生成，策略: '{strategy}' ---")
        # Removed print(f"  使用奇偶规则判断材料内部: {'是' if use_even_odd_rule else '否'}")

        if strategy.lower() == 'direct_offset': # 直接偏置策略
            # 实际边界内缩量，如果未提供则默认为行距的一半
            actual_offset_from_bounds = offset_distance if offset_distance is not None else row_spacing / 2.0
            paths_list, spacing_data = self._create_direct_offset_paths(
                row_spacing=row_spacing,
                offset_from_bounds=actual_offset_from_bounds,
                max_segment_length=max_segment_length,
                proximity_threshold=proximity_threshold,
                adaptive_density=adaptive_density,
                # Removed old adaptive parameters from call
                # min_spacing_factor=min_spacing_factor,
                # use_normal_component_method=use_normal_component_method,
                # ny_calculation_strategy=ny_calculation_strategy,
                # Pass new iteration parameters
                iter_min_delta_y_factor=iter_min_delta_y_factor,
                iter_max_delta_y_factor=iter_max_delta_y_factor,
                iter_tolerance_abs=iter_tolerance_abs,
                iter_max_iterations_per_step=iter_max_iterations_per_step,
                iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc
                # Removed use_even_odd_rule=use_even_odd_rule
            )
            return paths_list, spacing_data # 返回路径列表和间距数据
        
        elif strategy.lower() == 'projection': # 投影策略
            print(f"  使用投影策略，2D填充模式: '{projection_fill_pattern}'")
            all_boundary_contour_info = self.get_projected_boundary_contours() # 获取投影的2D边界轮廓
            if not all_boundary_contour_info:
                print("未能获取任何边界轮廓 (投影策略)，无法继续。")
                return [], [] # Return tuple like direct_offset
            
            external_contours = []
            # internal_contours_associated_with_external = {} # Store internal contours per external one

            for contour_data in all_boundary_contour_info:
                contour_2d = contour_data['points_2d']
                try:
                    contour_poly = Polygon(contour_2d)
                    if not contour_poly.is_valid:
                        contour_poly = contour_poly.buffer(0)
                except Exception as e:
                    print(f"警告: 处理轮廓 {contour_data['id']} 时出错: {e}")
                    continue
                
                is_internal = False
                for other_data in all_boundary_contour_info:
                    if other_data['id'] == contour_data['id']: continue
                    try:
                        other_poly = Polygon(other_data['points_2d'])
                        if not other_poly.is_valid: other_poly = other_poly.buffer(0)
                        if other_poly.contains(contour_poly):
                            is_internal = True
                            # Associate internal contour with its containing external contour
                            # Ensure 'inner_contours' list exists for the external contour_data
                            if 'inner_contours' not in other_data:
                                other_data['inner_contours'] = []
                            # Check for duplicates before adding
                            is_duplicate_inner = False
                            for existing_inner in other_data['inner_contours']:
                                if np.array_equal(existing_inner, contour_2d):
                                    is_duplicate_inner = True
                                    break
                            if not is_duplicate_inner:
                                other_data['inner_contours'].append(contour_2d)
                            break 
                    except Exception as e:
                        print(f"警告: 比较轮廓 {contour_data['id']} 和 {other_data['id']} 时出错: {e}")
                        continue
                if not is_internal:
                    external_contours.append(contour_data)
            
            print(f"共识别出 {len(external_contours)} 个外部轮廓。")
            
            final_paths_for_all_contours = []
            for b_data in all_boundary_contour_info:
                b_points_3d = b_data['points_3d']
                if len(b_points_3d) >= self.min_points_req:
                    # Use batch normal calculation if available and preferred
                    b_normals_3d = self.get_surface_normals_batch(b_points_3d)
                    if b_normals_3d is None or len(b_normals_3d) != len(b_points_3d):
                        print(f"警告: 边界路径 {b_data['id']} 法线计算失败，将逐点计算。")
                        b_normals_3d = np.array([self.get_surface_normal_at_point(p) for p in b_points_3d])
                    final_paths_for_all_contours.append((b_points_3d, b_normals_3d, True, b_data['id']))
                else:
                    print(f"边界路径 {b_data['id']} 点数不足 ({len(b_points_3d)})，跳过。")

            base_fill_segment_id_start = -10000 
            id_step_per_contour_fill = 10000
            target_3d_row_spacing = row_spacing 
            target_3d_offset_for_adjustment_calc = offset_distance if offset_distance is not None else target_3d_row_spacing / 2.0

            for contour_index, ex_contour_data in enumerate(external_contours):
                print(f"\n--- (投影策略) 处理外部轮廓 {ex_contour_data['id']} (索引 {contour_index}) 的填充 ---")
                current_contour_2d_points = ex_contour_data['points_2d']
                if len(current_contour_2d_points) < 3:
                    print(f"轮廓 {ex_contour_data['id']} 点数不足，无法生成2D填充。")
                    continue

                inner_contours_for_this_external = ex_contour_data.get('inner_contours', [])
                if inner_contours_for_this_external:
                    print(f"  轮廓 {ex_contour_data['id']} 将使用 {len(inner_contours_for_this_external)} 个内部孔洞进行填充排除。")

                # Check if _calculate_adjusted_spacing_parameters exists, if not, use fixed spacing
                if hasattr(self, '_calculate_adjusted_spacing_parameters'):
                    adjusted_2d_row_spacing, adjusted_2d_offset_for_fill = self._calculate_adjusted_spacing_parameters(
                        current_contour_2d_points,
                        target_3d_row_spacing,
                        target_3d_offset_for_adjustment_calc 
                    )
                else:
                    print("警告: _calculate_adjusted_spacing_parameters 方法未找到，将使用固定的2D间距和偏移。")
                    adjusted_2d_row_spacing = target_3d_row_spacing
                    adjusted_2d_offset_for_fill = offset_distance if offset_distance is not None else row_spacing / 2.0
                
                fill_points_2d, connection_types, offset_polygon_shapely = None, None, None
                if projection_fill_pattern.lower() == 'raster':
                    if not hasattr(self, 'generate_2d_raster_fill'):
                        print("错误: generate_2d_raster_fill 方法未找到!")
                        continue
                    fill_points_2d, connection_types, offset_polygon_shapely = self.generate_2d_raster_fill(
                        current_contour_2d_points, adjusted_2d_row_spacing, 
                        offset_distance=adjusted_2d_offset_for_fill, 
                        max_segment_length=max_segment_length,
                        inner_contours_list=inner_contours_for_this_external
                    )
                elif projection_fill_pattern.lower() == 'concentric':
                    if not hasattr(self, 'generate_2d_concentric_fill'):
                        print("错误: generate_2d_concentric_fill 方法未找到!")
                        continue
                    fill_points_2d, connection_types, offset_polygon_shapely = self.generate_2d_concentric_fill(
                        current_contour_2d_points, adjusted_2d_row_spacing, 
                        offset_distance=adjusted_2d_offset_for_fill, 
                        max_segment_length=max_segment_length,
                        inner_contours_list=inner_contours_for_this_external
                    )
                else:
                    print(f"错误: 未知的2D填充模式 '{projection_fill_pattern}'.")
                    continue

                # if fill_points_2d is not None and len(fill_points_2d) > 0:
                #     if hasattr(self, 'visualize_2d_fill'):
                #         self.visualize_2d_fill(current_contour_2d_points, offset_polygon_shapely, fill_points_2d, connection_types, row_spacing=adjusted_2d_row_spacing)
                #     pass 
                # else:
                #     print(f"轮廓 {ex_contour_data['id']} 未能生成2D填充路径。")
                #     continue 
                
                # Ensure project_2d_points_to_3d_surface and build_final_3d_paths exist
                if not hasattr(self, 'project_2d_points_to_3d_surface') or not hasattr(self, 'build_final_3d_paths'):
                    print("错误: 缺少 project_2d_points_to_3d_surface 或 build_final_3d_paths 方法!")
                    continue

                projected_3d_pts, projected_3d_normals, success_mask = self.project_2d_points_to_3d_surface(fill_points_2d)
                if projected_3d_pts is None or len(projected_3d_pts) == 0:
                    print(f"轮廓 {ex_contour_data['id']} 未能将任何2D填充点投影到3D曲面。")
                    continue 

                current_contour_fill_id_start = base_fill_segment_id_start - (contour_index * id_step_per_contour_fill)
                fill_segments_for_this_contour = self.build_final_3d_paths(
                    projected_3d_pts, projected_3d_normals, success_mask, 
                    fill_points_2d, connection_types, 
                    boundary_paths_data=None, 
                    projected_segment_id_start=current_contour_fill_id_start 
                )
                
                if fill_segments_for_this_contour:
                    final_paths_for_all_contours.extend(fill_segments_for_this_contour)
            
            print(f"\n投影切片流程完成，共生成 {len(final_paths_for_all_contours)} 个路径段。")
            return final_paths_for_all_contours, [] # Return tuple: (paths_list, spacing_data_empty_for_projection)
        else:
            print(f"错误：当前版本只支持 'direct_offset' 策略，不支持 '{strategy}'。")
            return [], [] # 返回空列表以匹配期望的返回类型

    def _get_3d_strips_for_single_offset_val(self, offset_val, offset_dir_axis, 
                                           boundary_shapely_linestring, boundary_kdtree, 
                                           inner_contours_shapely_list, 
                                           proximity_threshold, max_segment_length):
        """
        Generates 3D path strips for a single given offset value.
        MODIFIED: Uses direct 3D slicing instead of 2D intersection for initial strip generation.
        REMOVED: Logic for splitting strips based on `inner_contours_shapely_list` (buffered holes) 
                 as this is now handled later by `_segment_strip_by_3d_intersections`.

        Args:
            offset_val (float): The offset value (e.g., Y-coordinate) for which to generate paths.
            offset_dir_axis (int): The axis of offsetting (0 for X, 1 for Y).
            boundary_shapely_linestring (shapely.LineString): Main boundary (not directly used here anymore).
            boundary_kdtree (scipy.spatial.KDTree): KDTree for boundary (not directly used here anymore).
            inner_contours_shapely_list (list): Buffered 2D hole polygons (NO LONGER USED for splitting here).
            proximity_threshold (float): Min distance (NO LONGER USED for splitting here).
            max_segment_length (float): Maximum segment length for interpolating 3D paths.

        Returns:
            list: A list of numpy arrays, where each array is a 3D path strip.
        """
        generated_3d_strips = []
        
        # 1. Perform direct 3D slicing using self.mesh.section()
        plane_normal = np.zeros(3)
        plane_normal[offset_dir_axis] = 1.0
        plane_origin = self.mesh.centroid.copy() # Use mesh centroid as a base for plane origin
        plane_origin[offset_dir_axis] = offset_val

        try:
            path3d_slice_object = self.mesh.section(plane_origin=plane_origin, plane_normal=plane_normal)
        except Exception as e:
            print(f"    WARNING (_get_3d_strips): Error during self.mesh.section() at offset {offset_val:.3f}: {e}")
            return [] # Return empty if slicing fails

        initial_3d_polylines = []
        if path3d_slice_object is not None:
            if path3d_slice_object.discrete: # section can return a list of polylines
                initial_3d_polylines.extend(path3d_slice_object.discrete)
            elif path3d_slice_object.vertices is not None and len(path3d_slice_object.vertices) >= 2:
                initial_3d_polylines.append(path3d_slice_object.vertices)

        if not initial_3d_polylines:
            return []

        # Process each 3D polyline obtained from the slice
        for polyline_3d_initial_pts in initial_3d_polylines:
            if len(polyline_3d_initial_pts) < 2: 
                continue

            # Interpolate the raw 3D path strip
            # Normals will be calculated later if needed, or by the consuming function.
            interpolated_3d_path = self.interpolate_path_points(polyline_3d_initial_pts, max_segment_length)
            
            if len(interpolated_3d_path) >= self.min_points_req:
                generated_3d_strips.append(interpolated_3d_path)
        
        # Debugging print (can be removed or kept as needed)
        if offset_dir_axis == 0: # Example for X-axis offset debugging
            if generated_3d_strips:
                print(f"    DEBUG _get_3d_strips: Generated {len(generated_3d_strips)} raw 3D strips for offset_val(X)={offset_val:.4f} (hole avoidance removed from this stage).")
            else:
                print(f"    DEBUG _get_3d_strips: No raw 3D strips generated for offset_val(X)={offset_val:.4f} (hole avoidance removed from this stage).")

        return generated_3d_strips

    def save_spacing_analysis_to_file(self, spacing_data, output_csv_file):
        """
        将间距分析数据保存到CSV文件。

        参数:
        spacing_data: 包含间距分析字典的列表。
        output_csv_file: 输出CSV文件的路径。
        """
        if not spacing_data:
            print(f"警告: 没有间距分析数据可保存到 {output_csv_file}。")
            return

        fieldnames = [
            'Offset1_mm', 'Offset2_mm', 'ProjectedStep_mm', 
            'Target3DSpacing_mm', 'ActualAvg3DSpacing_mm', 'Error_mm', 
            'Min3DSpacing_mm', 'Max3DSpacing_mm', 'Median3DSpacing_mm', 
            'StdDev3DSpacing_mm', 'NumSamples'
        ]

        try:
            with open(output_csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for row_dict in spacing_data:
                    # 格式化浮点数以获得一致的输出
                    formatted_row = {k: (f"{v:.4f}" if isinstance(v, float) and not np.isnan(v) else v) for k, v in row_dict.items()}
                    writer.writerow(formatted_row)
            print(f"间距分析数据已成功保存到: {output_csv_file}")
        except IOError as e:
            print(f"错误: 无法写入间距分析文件 {output_csv_file}: {e}")
        except Exception as e:
            print(f"错误: 保存间距分析数据时发生未知错误: {e}")

    def generate_adaptive_slice_positions_iterative(self, start_offset, end_offset, 
                                                    target_3d_spacing_objective, 
                                                    offset_dir_axis, 
                                                    # Args for _get_3d_strips_for_single_offset_val
                                                    boundary_shapely_linestring, 
                                                    inner_contours_shapely_list, 
                                                    proximity_threshold, 
                                                    max_segment_length,
                                                    # Iteration control params
                                                    min_delta_y_factor=0.05, 
                                                    max_delta_y_factor=2.0, # Added max factor
                                                    tolerance_abs=0.1, # Absolute tolerance in mm
                                                    iter_max_iterations_per_step=15, # Added missing parameter
                                                    iter_num_samples_for_spacing_calc=7 # Renamed for consistency
                                                    # Removed use_even_odd_rule
                                                    ):
        """
        Generates adaptive slice positions using an iterative feedback loop to achieve 
        a target 3D spacing between path strips.

        Args:
            start_offset (float): Starting offset value.
            end_offset (float): Ending offset value.
            target_3d_spacing_objective (float): The desired 3D distance between path strips.
            offset_dir_axis (int): The axis of offsetting (0 for X, 1 for Y).
            boundary_shapely_linestring (shapely.LineString): Main boundary for path generation.
            inner_contours_shapely_list (list): List of Shapely Polygons for inner holes (buffered).
            proximity_threshold (float): Min distance from boundaries/holes for path generation.
            max_segment_length (float): Max segment length for interpolating 3D paths.
            min_delta_y_factor (float): Min allowed step (delta_y) as a factor of target_3d_spacing_objective.
            max_delta_y_factor (float): Max allowed step (delta_y) as a factor of target_3d_spacing_objective.
            tolerance_abs (float): Absolute tolerance (in mm) for matching the target 3D spacing.
            iter_max_iterations_per_step (int): Max iterations to find delta_y for each step.
            iter_num_samples_for_spacing_calc (int): Samples for _calculate_actual_3d_spacing_between_strip_sets.
        """
        if start_offset >= end_offset or target_3d_spacing_objective <= 0:
            return []

        print(f"\n  基于迭代反馈生成自适应切片位置:")
        print(f"    目标3D间距: {target_3d_spacing_objective:.3f}mm")
        print(f"    偏置轴: {'X' if offset_dir_axis == 0 else 'Y'}")
        print(f"    迭代参数: MinΔYFactor={min_delta_y_factor}, MaxΔYFactor={max_delta_y_factor}, AbsTolerance={tolerance_abs}mm, MaxIterPerStep={iter_max_iterations_per_step}")
        # Removed print(f"    使用奇偶规则: {'是' if use_even_odd_rule else '否'}")

        slice_positions = [start_offset] # Start with the initial offset
        current_pos = start_offset
        total_iterations_overall = 0
        max_overall_iterations = 2000 # Safety break for the entire process

        # Get initial strips for the starting position
        # Replaced placeholder _get_raw_or_simplified_strips_at_offset with _get_3d_strips_for_single_offset_val
        # boundary_kdtree argument is not used by _get_3d_strips_for_single_offset_val, so passing None is fine.
        strips_at_current_pos = self._get_3d_strips_for_single_offset_val(
                current_pos, offset_dir_axis, 
                boundary_shapely_linestring, None, # boundary_kdtree is None
                inner_contours_shapely_list, # These are buffered hole polygons
                proximity_threshold, max_segment_length
            )


        if not strips_at_current_pos:
            print(f"    警告: 在起始位置 {current_pos:.3f} 未能生成路径条带。无法继续。")
            return slice_positions # Return just the start if no strips generated

        min_delta_y_abs = target_3d_spacing_objective * min_delta_y_factor
        max_delta_y_abs = target_3d_spacing_objective * max_delta_y_factor
        scan_axis = 1 - offset_dir_axis

        while current_pos < end_offset and total_iterations_overall < max_overall_iterations:
            total_iterations_overall += 1
            best_delta_y = None
            best_measured_spacing = float('inf')
            strips_at_best_next_pos = None
            final_next_pos_for_this_step = None

            # Initial guess for delta_y: aim for target_3d_spacing_objective directly
            # A more sophisticated initial guess could use normal components if available/reliable.
            delta_y_trial = target_3d_spacing_objective 
            delta_y_trial = np.clip(delta_y_trial, min_delta_y_abs, max_delta_y_abs)

            print(f"  迭代寻找 {current_pos:.3f}mm 后的下一个切片位置...")
            
            for iter_count in range(iter_max_iterations_per_step):
                next_pos_trial = current_pos + delta_y_trial
                if next_pos_trial >= end_offset + min_delta_y_abs: # Don't overshoot too much
                    print(f"    尝试ΔY={delta_y_trial:.4f} (下一位置 {next_pos_trial:.3f}) 超出结束点 {end_offset:.3f}，结束此步迭代。")
                    # If this is the first trial and it overshoots, we might not find a best_delta_y
                    if best_delta_y is None and next_pos_trial > current_pos + min_delta_y_abs / 2 : # Ensure it's a valid step
                        # Potentially add end_offset if it's a valid step, handled later
                        pass 
                    break 

                strips_at_next_pos_trial = None # Initialize before assignment

                # Replaced placeholder _get_raw_or_simplified_strips_at_offset with _get_3d_strips_for_single_offset_val
                strips_at_next_pos_trial = self._get_3d_strips_for_single_offset_val(
                    next_pos_trial, offset_dir_axis,
                    boundary_shapely_linestring, None, # boundary_kdtree is None
                    inner_contours_shapely_list, # These are buffered hole polygons
                    proximity_threshold, max_segment_length
                )

                if not strips_at_next_pos_trial:
                    print(f"    尝试ΔY={delta_y_trial:.4f} (下一位置 {next_pos_trial:.3f}): 未能生成路径条带。尝试减小ΔY。")
                    delta_y_trial *= 0.75 # Reduce delta_y and try again
                    delta_y_trial = max(delta_y_trial, min_delta_y_abs) # Ensure it doesn't go below min
                    if iter_count == iter_max_iterations_per_step -1 and best_delta_y is None:
                         print(f"    在最后一次尝试减小ΔY后仍无法生成条带。")
                    continue

                spacing_stats = self._calculate_actual_3d_spacing_between_strip_sets(
                    strips_at_current_pos, strips_at_next_pos_trial, 
                    scan_axis, num_samples_on_strip1=iter_num_samples_for_spacing_calc
                )
                
                measured_dist_iter = spacing_stats['avg_dist'] # Using average distance for feedback
                if np.isnan(measured_dist_iter) or spacing_stats['num_samples'] == 0:
                    print(f"    尝试ΔY={delta_y_trial:.4f} (下一位置 {next_pos_trial:.3f}): 无法计算3D间距 (样本数: {spacing_stats['num_samples']})。尝试调整ΔY。")
                    # If spacing calc fails, could be due to too large or too small delta_y. 
                    # Heuristic: if previous measured_dist was too large, reduce delta_y, else increase.
                    if best_measured_spacing != float('inf') and best_measured_spacing > target_3d_spacing_objective:
                         delta_y_trial *= 0.85
                    else:
                         delta_y_trial *= 1.15
                    delta_y_trial = np.clip(delta_y_trial, min_delta_y_abs, max_delta_y_abs)
                    if iter_count == iter_max_iterations_per_step -1 and best_delta_y is None:
                        print(f"    在最后一次尝试调整ΔY后仍无法计算间距。")
                    continue

                error_abs = abs(measured_dist_iter - target_3d_spacing_objective)
                print(f"    迭代 {iter_count+1}: ΔY_trial={delta_y_trial:.4f} -> 3D间距={measured_dist_iter:.4f}mm (误差={error_abs:.4f}mm)")

                if error_abs < tolerance_abs:
                    best_delta_y = delta_y_trial
                    strips_at_best_next_pos = strips_at_next_pos_trial
                    final_next_pos_for_this_step = next_pos_trial
                    print(f"      满足容差! 选定 ΔY={best_delta_y:.4f}")
                    break # Found a good enough delta_y
                
                # Update if this is the best one found so far (closest to target)
                if error_abs < abs(best_measured_spacing - target_3d_spacing_objective):
                    best_delta_y = delta_y_trial
                    best_measured_spacing = measured_dist_iter
                    strips_at_best_next_pos = strips_at_next_pos_trial
                    final_next_pos_for_this_step = next_pos_trial

                # Adjust delta_y_trial for next iteration (proportional feedback)
                if measured_dist_iter > 1e-6: # Avoid division by zero or tiny numbers
                    adjustment_factor = target_3d_spacing_objective / measured_dist_iter
                    delta_y_trial *= adjustment_factor
                    delta_y_trial = np.clip(delta_y_trial, min_delta_y_abs, max_delta_y_abs)
                else: # measured_dist_iter is too small or zero, significantly increase delta_y
                    delta_y_trial *= 1.5 
                    delta_y_trial = np.clip(delta_y_trial, min_delta_y_abs, max_delta_y_abs)
            
            # After iteration loop for one step
            if best_delta_y is not None:
                print(f"    本步最终选择: ΔY={best_delta_y:.4f}mm, 下一切片位置: {final_next_pos_for_this_step:.3f}mm")
                current_pos = final_next_pos_for_this_step
                slice_positions.append(current_pos)
                strips_at_current_pos = strips_at_best_next_pos # For the next iteration's "current strips"
                if not strips_at_current_pos: # Should not happen if best_delta_y was set
                    print(f"    严重警告: strips_at_best_next_pos 为空，但 best_delta_y 已设置。流程可能中断。")
                    break
            else:
                # No suitable delta_y found within iterations, or all trials overshot/
                # Try to add end_offset if it's a reasonable step from current_pos
                print(f"    未能通过迭代找到理想的ΔY。")
                if current_pos < end_offset and (end_offset - current_pos) >= min_delta_y_abs / 2 : # Ensure end_offset is a valid next step
                    print(f"    尝试将结束点 {end_offset:.3f}mm 作为最后一个切片位置。")
                    slice_positions.append(end_offset)
                break # Stop the adaptive slicing process for this region

        # Final check on the last position
        if slice_positions and slice_positions[-1] < end_offset and (end_offset - slice_positions[-1]) < min_delta_y_abs / 2 and (end_offset - slice_positions[-1]) > 1e-3:
             # If the last actual position is very close to end_offset but not quite, update it to end_offset
            print(f"    微调最后一个切片位置从 {slice_positions[-1]:.3f} 到 {end_offset:.3f}")
            slice_positions[-1] = end_offset
        elif slice_positions and slice_positions[-1] > end_offset: # If overshot, remove last if it is not the only one after start
            if len(slice_positions) > 1 and slice_positions[-1] > end_offset + tolerance_abs : # only remove if significantly over
                print(f"    最后一个计算的切片位置 {slice_positions[-1]:.3f} 超出了结束点 {end_offset:.3f}，正在移除。")
                slice_positions.pop()
                # Optionally, add end_offset if the new last position is far from it.
                if slice_positions and (end_offset - slice_positions[-1]) >= min_delta_y_abs:
                    slice_positions.append(end_offset)
            elif len(slice_positions) == 1 and slice_positions[0] > end_offset: # start_offset itself is beyond end_offset
                print(f"    起始切片 {slice_positions[0]:.3f} 已超出结束点 {end_offset:.3f}，列表将为空。")
                return []
        
        print(f"  迭代自适应切片生成完成: {len(slice_positions)} 个位置")
        if len(slice_positions) > 1:
            avg_spacing_actual = (slice_positions[-1] - slice_positions[0]) / (len(slice_positions) - 1)
            print(f"    实际平均偏置轴间距: {avg_spacing_actual:.4f}mm")
        elif len(slice_positions) == 1:
             print(f"    只生成了一个切片位置: {slice_positions[0]:.3f}mm")
        else:
            print(f"    未生成任何切片位置。")
        
        return slice_positions

    
    def _is_3d_segment_inside_material_batch(self, points_3d_array):
        """
        批量判断3D点是否在材料内部
        
        Args:
            points_3d_array (numpy.ndarray): 3D点数组，形状为 (n, 3)
            
        Returns:
            numpy.ndarray: 布尔数组，True表示对应点在材料内部
        """
        try:
            # 使用trimesh的批量contains方法
            inside_mask = self.mesh.contains(points_3d_array)
            return inside_mask
        except Exception as e:
            print(f"警告: 批量3D材料内部判断失败: {e}")
            return np.zeros(len(points_3d_array), dtype=bool)

    def _segment_strip_by_3d_intersections(self, strip_points_input, strip_normals_input, all_3d_boundary_linestrings):
        """
        Segments a 3D path strip based on its intersections with 3D boundary polylines.
        Uses an even-odd rule to identify fill segments.

        Args:
            strip_points_input (np.ndarray): The 3D points of the path strip.
            strip_normals_input (np.ndarray): The corresponding 3D normals for the strip points.
            all_3d_boundary_linestrings (list): List of Shapely LineString objects representing 3D boundaries.

        Returns:
            list: A list of dictionaries, where each dictionary is a fill segment:
                  {'points': np.ndarray, 'normals': np.ndarray}
        """
        fill_segments_found = []
        if len(strip_points_input) < self.min_points_req:
            return fill_segments_found

        # Helper to convert 3D points to Shapely LineString (can be defined locally or be a class method)
        def to_shapely_linestring_3d_local(points):
            if len(points) < 2:
                return None
            return LineString(points)

        current_strip_shapely_3d = to_shapely_linestring_3d_local(strip_points_input)
        if not current_strip_shapely_3d:
            # print("DEBUG _segment_strip: Could not convert input strip to LineString.") # Optional
            return fill_segments_found

        if not all_3d_boundary_linestrings: # No boundaries to intersect with
            # print("DEBUG _segment_strip: No 3D boundaries provided. Returning original strip as fill.") # Optional
            fill_segments_found.append({'points': strip_points_input, 'normals': strip_normals_input})
            return fill_segments_found

        intersections_on_strip = [] # List to store (distance_along_strip, intersection_point_3d)
        for boundary_line_3d in all_3d_boundary_linestrings:
            if current_strip_shapely_3d.intersects(boundary_line_3d):
                intersection_geom = current_strip_shapely_3d.intersection(boundary_line_3d)
                
                def extract_points_from_geom_local(geom):
                    # (Same extract_points_from_geom logic as previously designed)
                    extracted = []
                    if geom.is_empty: return extracted
                    if geom.geom_type == 'Point': extracted.append(geom)
                    elif geom.geom_type == 'MultiPoint': extracted.extend(list(geom.geoms))
                    elif geom.geom_type == 'LineString':
                        coords = list(geom.coords)
                        if coords: extracted.append(Point(coords[0]));
                        if len(coords) > 1: extracted.append(Point(coords[-1]))
                    elif geom.geom_type == 'MultiLineString':
                        for ls_geom in geom.geoms:
                            coords = list(ls_geom.coords)
                            if coords: extracted.append(Point(coords[0]));
                            if len(coords) > 1: extracted.append(Point(coords[-1]))
                    elif geom.geom_type == 'GeometryCollection':
                        for sub_geom in geom.geoms: extracted.extend(extract_points_from_geom_local(sub_geom))
                    return extracted

                actual_intersection_points_shapely = extract_points_from_geom_local(intersection_geom)
                for int_point_shapely in actual_intersection_points_shapely:
                    distance_on_strip = current_strip_shapely_3d.project(int_point_shapely)
                    projected_on_strip_shapely = current_strip_shapely_3d.interpolate(distance_on_strip)
                    is_duplicate = any(abs(dist_existing - distance_on_strip) < 1e-5 for dist_existing, _ in intersections_on_strip)
                    if not is_duplicate:
                        intersections_on_strip.append((distance_on_strip, np.array(projected_on_strip_shapely.coords[0])))
        
        intersections_on_strip.sort(key=lambda x: x[0])

        if not intersections_on_strip:
            # print("DEBUG _segment_strip: Strip does not intersect any boundaries. Returning as single fill segment.") # Optional
            # Assumption: if a strip doesn't intersect, it's considered inside the "mesh face".
            fill_segments_found.append({'points': strip_points_input, 'normals': strip_normals_input})
            return fill_segments_found

        # print(f"DEBUG _segment_strip: Found {len(intersections_on_strip)} sorted intersections.") # Optional
        segmentation_points_3d = [strip_points_input[0]]
        for _, int_pt_3d in intersections_on_strip:
            segmentation_points_3d.append(int_pt_3d)
        if not np.allclose(strip_points_input[-1], segmentation_points_3d[-1]):
            segmentation_points_3d.append(strip_points_input[-1])

        refined_segment_pts_with_dist = []
        if current_strip_shapely_3d.length > 1e-6:
            for pt_3d in segmentation_points_3d:
                dist = current_strip_shapely_3d.project(Point(pt_3d))
                pt_on_line_exact = np.array(current_strip_shapely_3d.interpolate(dist).coords[0])
                if not any(abs(d_existing - dist) < 1e-5 for d_existing, _ in refined_segment_pts_with_dist):
                    refined_segment_pts_with_dist.append((dist, pt_on_line_exact))
        
        refined_segment_pts_with_dist.sort(key=lambda x: x[0])
        final_segmentation_points_3d = [pt for _, pt in refined_segment_pts_with_dist]

        is_fill_segment_type = True # First segment (strip_start to 1st_int) is FILL
        for i_seg_pt in range(len(final_segmentation_points_3d) - 1):
            p_start_sub = final_segmentation_points_3d[i_seg_pt]
            p_end_sub = final_segmentation_points_3d[i_seg_pt + 1]
            
            sub_segment_final_points = [p_start_sub]
            start_dist_sub = current_strip_shapely_3d.project(Point(p_start_sub))
            end_dist_sub = current_strip_shapely_3d.project(Point(p_end_sub))

            # Add original points from strip_points_input that fall strictly between p_start_sub and p_end_sub
            # This ensures we use the original interpolated points from the strip where possible.
            for k_orig_pt_idx in range(len(strip_points_input)):
                original_pt = strip_points_input[k_orig_pt_idx]
                if np.allclose(original_pt, p_start_sub) or np.allclose(original_pt, p_end_sub):
                    continue
                dist_orig_pt = current_strip_shapely_3d.project(Point(original_pt))
                # Check if dist_orig_pt is strictly between start_dist_sub and end_dist_sub with a small tolerance
                if (start_dist_sub < dist_orig_pt - 1e-6) and (dist_orig_pt < end_dist_sub - 1e-6):
                     # Further check: ensure this original point is not already too close to an intersection point
                     # to avoid including points that are essentially the intersection points themselves.
                    is_close_to_segmentation_pt = False
                    for seg_pt_check in final_segmentation_points_3d:
                        if np.allclose(original_pt, seg_pt_check, atol=1e-4):
                            is_close_to_segmentation_pt = True
                            break
                    if not is_close_to_segmentation_pt:
                        sub_segment_final_points.append(original_pt)
            
            if not np.allclose(p_end_sub, sub_segment_final_points[-1]):
                 sub_segment_final_points.append(p_end_sub)
            
            # Re-sort collected points for this sub-segment by their distance along the original strip
            # to ensure correct order before creating the final numpy array.
            sub_segment_points_with_dist_temp = []
            for pt_in_sub in sub_segment_final_points:
                dist_on_strip = current_strip_shapely_3d.project(Point(pt_in_sub))
                # Avoid duplicates based on distance again, very locally for this sub-segment
                if not any(abs(existing_d[0] - dist_on_strip) < 1e-5 for existing_d in sub_segment_points_with_dist_temp):
                    sub_segment_points_with_dist_temp.append((dist_on_strip, pt_in_sub))
            
            sub_segment_points_with_dist_temp.sort(key=lambda x: x[0])
            current_sub_segment_np = np.array([pt for _, pt in sub_segment_points_with_dist_temp])
            
            if len(current_sub_segment_np) >= self.min_points_req:
                if is_fill_segment_type:
                    # Get corresponding normals. This needs careful slicing from strip_normals_input
                    # or re-calculating if points were significantly altered.
                    # For now, we assume a direct mapping from original points or re-calculate.
                    # The safest is to re-calculate normals for the exact points in current_sub_segment_np.
                    sub_segment_normals = self.get_surface_normals_batch(current_sub_segment_np)
                    if sub_segment_normals is not None and len(sub_segment_normals) == len(current_sub_segment_np):
                        fill_segments_found.append({
                            'points': current_sub_segment_np,
                            'normals': sub_segment_normals
                        })
                        # print(f"DEBUG _segment_strip: Added FILL sub-segment with {len(current_sub_segment_np)} points.") # Optional
            is_fill_segment_type = not is_fill_segment_type
        
        # print(f"DEBUG _segment_strip: Returning {len(fill_segments_found)} fill segments.") # Optional
        return fill_segments_found

    def _trim_path_ends(self, path_points, trim_length):
        """
        Trims a specified length from both ends of a 3D path.
        
        Args:
            path_points (np.ndarray): The 3D points of the path strip.
            trim_length (float): The length to trim from each end.
            
        Returns:
            np.ndarray: The trimmed path points, or the original if too short to trim / becomes too short.
        """
        if path_points is None or len(path_points) < self.min_points_req or trim_length <= 1e-6:
            return path_points # Return original if no points, not enough points, or no trim length

        # Calculate cumulative lengths along the path
        segment_lengths = np.linalg.norm(np.diff(path_points, axis=0), axis=1)
        cumulative_lengths = np.concatenate(([0], np.cumsum(segment_lengths)))
        total_path_length = cumulative_lengths[-1]

        if total_path_length <= 2 * trim_length: # Path is too short to trim from both ends
            # Consider if it should return empty or original. For now, original.
            # If the requirement is strict that segments shorter than 2*trim_length are invalid after this,
            # then one might return np.array([]) here if total_path_length < self.min_points_req * some_min_segment_len
            return path_points 

        # --- Trim Start --- 
        points_after_start_trim_list = list(path_points)
        new_start_idx_offset = 0
        for i in range(len(cumulative_lengths)):
            if cumulative_lengths[i] >= trim_length:
                if i > 0: # Interpolate to find the new start point
                    p_b, p_a = path_points[i-1], path_points[i]
                    len_b, len_a = cumulative_lengths[i-1], cumulative_lengths[i]
                    seg_len = len_a - len_b
                    if seg_len > 1e-9:
                        t = (trim_length - len_b) / seg_len
                        t = np.clip(t, 0.0, 1.0)
                        new_start_pt = p_b + t * (p_a - p_b)
                        points_after_start_trim_list = [new_start_pt] + list(path_points[i:])
                        new_start_idx_offset = i # The original index from which points are kept (or new start point is before)
                    else: # Segment length is zero, effectively means points[i-1] and points[i] are same
                        points_after_start_trim_list = list(path_points[i:])
                        new_start_idx_offset = i
                else: # trim_length is 0 or very small, or first point itself is beyond trim_length
                    points_after_start_trim_list = list(path_points) # Keep all, effectively no trim from start
                    new_start_idx_offset = 0
                break
        else: # Loop finished without break, means path is shorter than trim_length from start
            return np.array([]) # Path is consumed by start trim

        current_trimmed_points = np.array(points_after_start_trim_list)
        if len(current_trimmed_points) < self.min_points_req:
             return np.array([]) # Became too short after start trim

        # --- Trim End --- 
        # Recalculate cumulative lengths for the (potentially start-trimmed) path
        segment_lengths_end = np.linalg.norm(np.diff(current_trimmed_points, axis=0), axis=1)
        cumulative_lengths_end = np.concatenate(([0], np.cumsum(segment_lengths_end)))
        total_path_length_after_start_trim = cumulative_lengths_end[-1]

        if total_path_length_after_start_trim <= trim_length: # Path is too short to trim from end (or consumed by start trim)
             # This implies the remaining path is shorter than or equal to one trim_length
             # If it was already start-trimmed, and now the remainder is <= trim_length,
             # it means the effective length is less than trim_length. So, empty.
            return np.array([]) 

        points_after_end_trim_list = list(current_trimmed_points)
        for i in range(len(cumulative_lengths_end) -1, -1, -1): # Iterate backwards
            length_from_end = total_path_length_after_start_trim - cumulative_lengths_end[i]
            if length_from_end >= trim_length:
                if i < len(cumulative_lengths_end) - 1: # Interpolate for new end point
                    # p_b is current_trimmed_points[i], p_a is current_trimmed_points[i+1]
                    # The new end point lies on the segment (points[i], points[i+1])
                    p_b_end, p_a_end = current_trimmed_points[i], current_trimmed_points[i+1]
                    # len_at_i_from_start = cumulative_lengths_end[i]
                    # len_at_i_plus_1_from_start = cumulative_lengths_end[i+1]
                    # We want a point on segment [p_b_end, p_a_end] such that its distance to total_path_length_after_start_trim is trim_length.
                    # Target length from start is total_path_length_after_start_trim - trim_length.
                    target_len_from_start_for_new_end = total_path_length_after_start_trim - trim_length
                    
                    seg_len_end = cumulative_lengths_end[i+1] - cumulative_lengths_end[i]
                    if seg_len_end > 1e-9:
                        t = (target_len_from_start_for_new_end - cumulative_lengths_end[i]) / seg_len_end
                        t = np.clip(t, 0.0, 1.0)
                        new_end_pt = p_b_end + t * (p_a_end - p_b_end)
                        points_after_end_trim_list = list(current_trimmed_points[:i+1]) + [new_end_pt]
                    else: # Segment length is zero
                        points_after_end_trim_list = list(current_trimmed_points[:i+1])
                else: # trim_length lands on or before the first point from end (i.e., current_trimmed_points[i])
                      # This case should ideally be handled by the total_path_length_after_start_trim <= trim_length check, 
                      # or if i is the last point, it means no trim from end based on this loop iteration.
                      # However, if i == len(cumulative_lengths_end) - 1, it implies the end point itself.
                      # For safety, if it reaches here, it implies the segment (current_trimmed_points[i], end) is being considered.
                      # If points[i] is the new end, then points_after_end_trim_list = list(current_trimmed_points[:i+1]) is correct.
                    points_after_end_trim_list = list(current_trimmed_points[:i+1]) 
                break
        else: # Loop finished without break, means path is shorter than trim_length from end
            return np.array([]) # Path is consumed by end trim
        
        final_trimmed_points = np.array(points_after_end_trim_list)
        if len(final_trimmed_points) < self.min_points_req:
            return np.array([]) # Became too short after end trim
            
        return final_trimmed_points




if __name__ == "__main__":
    # 确保 time 在此作用域内可用，尽管已经在顶部导入
    # 但为了 edit_file 的明确性，可以再次引用或假设其已在全局作用域
    script_total_start_time_main = time.time() # 记录脚本总开始时间

    # 定义要处理的多个网格文件列表
    input_mesh_files_list = [
        # "src/m19_mnp_003.stl", # 首先处理这个
        # "src/m19_mnp_002.stl", # 
        # "src/m19_mnp_001.stl", # 
        # "src/xiekua_mnp_003.stl",  # 然后处理这个
        # "src/xiekua_mnp_002.stl",  # 然后处理这个
        # "src/xiekua_mnp_001.stl",  # 然后处理这个
        # "src/m11_mnp_001.stl",
        # "src/m11_mnp_000.stl",
        # "src\m21_mnp_003.stl" ,# 然后处理这个
        # "src\m21_mnp_002.stl", # 然后处理这个
        "src\m21_mnp_001.stl", # 然后处理这个
        # "src/jiyi_mnp_002.stl", # 然后处理这个
        # "src/jiyi_mnp_001.stl", # 然后处理这个
        # "src/mengpi_mnp_000.stl", # 然后处理这个
    ]
    
    # --- G-code 模式选择 ---
    enable_rotation_axes = False  # True: 6轴G-code模式(包含ABC角度), False: 传统G-code模式(仅XYZ)
    
    output_paths_filename = "direct_projected_paths.txt" # 输出路径文本文件名
    
    # 根据模式选择输出文件名
    if enable_rotation_axes:
        output_gcode_filename = "direct_projected_paths_6axis.gcode" # 6轴G-code文件名
        print(f"--- Mode Selected: 6-Axis G-code Mode (with ABC rotation axes) ---")
    else:
        output_gcode_filename = "direct_projected_paths_traditional.gcode" # 传统G-code文件名
        print(f"--- Mode Selected: Traditional G-code Mode (XYZ coordinates only) ---")
    
    script_dir = os.path.dirname(os.path.abspath(__file__)) #脚本所在目录
    output_paths_file_abs = os.path.join(script_dir, output_paths_filename) # 输出路径文件的绝对路径
    output_gcode_file_abs = os.path.join(script_dir, output_gcode_filename) # 输出G-code文件的绝对路径
    
    target_bead_width = 0.6
    # G-code 生成参数
    gcode_target_extruder_temp = 220        # 挤出机目标温度 (°C) - 匹配正常文件
    gcode_target_bed_temp = 60              # 热床目标温度 (°C)
    gcode_filament_diameter = 1.75          # 耗材直径 (mm)
    gcode_nozzle_diameter = 0.6             # 喷嘴直径 (mm)
    gcode_extrusion_width = 0.4             # 挤出宽度 (mm) - 匹配正常文件
    gcode_layer_height = 0.4               # 层高 (mm) - 匹配正常文件
    gcode_retraction_amount = 0.8           # 回抽量 (mm) - 匹配正常文件
    gcode_retraction_feedrate = 1800        # 回抽速度 (mm/min) - 匹配正常文件
    gcode_print_feedrate = 3000             # 打印速度 (mm/min)
    gcode_travel_feedrate = 21000           # 空程速度 (mm/min) - 匹配正常文件
    gcode_extrusion_multiplier = 1.2       # 挤出倍率 (1.0 = 100%)
    gcode_slope_compensation_exponent = 0   # 斜率补偿指数 (0表示无补偿, 1表示1/cos(theta)补偿)

    gcode_normal_hop_distance = 2.0         # 法线方向跳跃距离 (mm)
    gcode_clearance_above_model_max = 3.0   # 模型上方最大间隙 (mm)
    gcode_rotation_feedrate = 5000          # 旋转轴进给速率 (mm/min or deg/min, 取决于固件)

    # --- 路径生成策略和参数 ---
    strategy_choice = 'direct_offset' # 选择 'projection' (投影) 策略
    # strategy_choice = 'direct_offset' # 注释掉 direct_offset

    # 投影策略的填充模式
    proj_fill_pattern_param = 'raster' # 可选 'raster' 或 'concentric'

    # 通用参数，还有个proximity_threshold = 0.5 # 毫米，点与边界的最小允许距离
    # 对于投影策略: 2D填充线间距。对于直接偏置策略: 3D条带间距。
    path_row_spacing = target_bead_width * 0.8
    # 用于插值的最大段长。
    path_max_segment_length = target_bead_width * 2 # Changed from target_bead_width * 2.0
    
    # 为投影策略单独设置最大段长参数
    projection_max_segment_length_param = target_bead_width * 0.5 # 投影策略使用更小的插值段长
    
    # 参数 'offset_distance_param' 的含义取决于策略:
    # 对于 'projection': 它是2D多边形填充区域的向内偏移。
    # 对于 'direct_offset': 它是沿偏置轴从网格边界的边距。
    offset_distance_param = target_bead_width / 2.0

    # 切片方向 (影响两种策略的X/Y轴选择)
    slicer_slice_direction = "y" # 'x' 或 'y'.对于直接偏置，这是我们步进的轴。
    
    # 边界和孔洞距离参数 (毫米) - 控制填充路径与边界/孔洞的最小距离
    proximity_threshold_param = 0  # 可调整此值来控制与边界和孔洞的距离

    # 特定于 'direct_offset' (直接偏置) 策略的自适应密度控制参数
    adaptive_density_enabled = True       # 启用自适应密度控制
         

    # New iterative slicer parameters in main
    iter_min_delta_y_factor_param = 0.05
    iter_max_delta_y_factor_param = 2.0
    iter_tolerance_abs_param = 0.01 # Absolute tolerance in mm
    iter_max_iterations_per_step_param = 5
    iter_num_samples_for_spacing_calc_param = 5

    print(f"--- 多文件路径生成参数 ---")
    print(f"  输入网格文件数量: {len(input_mesh_files_list)}")
    for i, mesh_file in enumerate(input_mesh_files_list, 1):
        print(f"  文件 {i}: {mesh_file}")
    print(f"  生成策略: {strategy_choice}")
    print(f"  切片/偏置方向轴: {slicer_slice_direction.upper()}")
    print(f"  目标珠宽: {target_bead_width:.3f} mm")
    print(f"  路径行距/条带间距: {path_row_spacing:.3f} mm")
    # print(f"  路径最大段长(插值用): {path_max_segment_length:.3f} mm") # 旧的通用打印方式
    if strategy_choice == 'direct_offset':
        print(f"  直接偏置 - 路径最大段长(插值用): {path_max_segment_length:.3f} mm")
    elif strategy_choice == 'projection':
        print(f"  投影 - 路径最大段长(插值用): {projection_max_segment_length_param:.3f} mm")
    print(f"  边界和孔洞距离阈值: {proximity_threshold_param:.3f} mm")
    if strategy_choice == 'direct_offset':
        print(f"  直接偏置策略 - 边界内缩量: {offset_distance_param:.3f} mm")
        print(f"  直接偏置策略 - 自适应密度: {'启用' if adaptive_density_enabled else '禁用'}")
        if adaptive_density_enabled:
            # 日志现在将只反映迭代自适应方法
            print(f"  直接偏置策略 - 使用迭代反馈自适应间距方法")
            print(f"    迭代参数: MinΔYFactor={iter_min_delta_y_factor_param:.2f}, MaxΔYFactor={iter_max_delta_y_factor_param:.2f}, AbsTol={iter_tolerance_abs_param:.2f}mm")
    elif strategy_choice == 'projection':
        print(f"  投影策略 - 2D边界内缩量: {offset_distance_param:.3f} mm")
        print(f"  投影策略 - 2D填充模式: {proj_fill_pattern_param}")
    print(f"-------------------------")

    # 存储所有文件的路径数据
    all_files_paths = []
    total_segments_count = 0
    
    print("\n--- 开始处理多个文件的路径生成过程 ---")
    slicing_start_time = time.time()
    
    # 处理每个文件
    for file_index, input_mesh_file in enumerate(input_mesh_files_list, 1):
        print(f"\n=== 正在处理文件 {file_index}/{len(input_mesh_files_list)}: {input_mesh_file} ===")
        
        # 检查文件是否存在
        if not os.path.exists(input_mesh_file):
            print(f"警告: 文件 {input_mesh_file} 不存在，跳过处理。")
            continue
            
        try:
            # 为当前文件创建切片器实例
            projector = DirectProjectionSlicer(
                mesh_path=input_mesh_file,
                target_surface_distance=target_bead_width, # 参考值，direct_offset策略不直接用于路径生成
                slice_direction=slicer_slice_direction,    # 切片/偏置方向
                inward_normals=True, # 法线朝内
                min_points_req=2      # 路径段所需最小点数
            )

            # 根据策略选择合适的max_segment_length
            current_max_segment_length = projection_max_segment_length_param if strategy_choice == 'projection' else path_max_segment_length

            # 生成当前文件的路径
            current_file_paths = projector.create_projected_fill_paths(
                row_spacing=path_row_spacing,
                offset_distance=offset_distance_param, 
                max_segment_length=current_max_segment_length, # 使用条件选择的max_segment_length
                strategy=strategy_choice,
                projection_fill_pattern=proj_fill_pattern_param, # 为投影策略传递填充模式
                proximity_threshold=proximity_threshold_param, 
                adaptive_density=adaptive_density_enabled, 
                # min_spacing_factor=min_spacing_factor_param, # Removed old param
                # use_normal_component_method=use_normal_component_method_param, # Removed old param
                # ny_calculation_strategy=ny_calculation_strategy_param, # Removed old param
                # Pass new iteration parameters from main to create_projected_fill_paths
                iter_min_delta_y_factor=iter_min_delta_y_factor_param,
                iter_max_delta_y_factor=iter_max_delta_y_factor_param,
                iter_tolerance_abs=iter_tolerance_abs_param,
                iter_max_iterations_per_step=iter_max_iterations_per_step_param,
                iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc_param
            )
            
            if current_file_paths: # current_file_paths is now a tuple (paths_list, spacing_data)
                paths_list_for_file = current_file_paths[0]
                spacing_data_for_file = current_file_paths[1]

                print(f"文件 {input_mesh_file} 成功生成 {len(paths_list_for_file)} 个路径段")
                
                # 为当前文件的路径段添加偏移量，避免segment_id冲突
                adjusted_paths = []
                segment_id_offset = file_index * 100000  # 每个文件使用不同的ID范围
                
                for points, normals, is_boundary, segment_id in paths_list_for_file:
                    # 调整segment_id以避免冲突
                    new_segment_id = segment_id + segment_id_offset
                    adjusted_paths.append((points, normals, is_boundary, new_segment_id))
                
                all_files_paths.extend(adjusted_paths)
                total_segments_count += len(paths_list_for_file)
                
                print(f"文件 {input_mesh_file} 的路径已添加到总集合中")

                # 保存当前文件的间距分析数据
                if spacing_data_for_file:
                    base_name, _ = os.path.splitext(os.path.basename(input_mesh_file))
                    spacing_output_filename = f"spacing_analysis_{base_name}.csv"
                    spacing_output_file_abs = os.path.join(script_dir, spacing_output_filename)
                    projector.save_spacing_analysis_to_file(spacing_data_for_file, spacing_output_file_abs)
                else:
                    print(f"文件 {input_mesh_file} 没有生成间距分析数据。")
                
            else: # current_file_paths was ([], []) or something indicating failure
                print(f"警告: 文件 {input_mesh_file} 未能生成任何路径段或间距数据")
                
        except Exception as e:
            print(f"错误: 处理文件 {input_mesh_file} 时发生异常: {e}")
            continue
    
    slicing_duration = time.time() - slicing_start_time
    print(f"--- 所有文件路径生成总耗时: {slicing_duration:.4f} 秒 ---")
    print(f"--- 总计处理了 {len(input_mesh_files_list)} 个文件，生成了 {total_segments_count} 个路径段 ---")

    if all_files_paths:
        print(f"\n成功合并所有文件的路径，总共 {len(all_files_paths)} 个路径段。正在保存和可视化...")
        
        # 创建一个代表性的切片器实例用于可视化和G-code生成（使用第一个有效文件）
        representative_projector = None
        for input_mesh_file in input_mesh_files_list:
            if os.path.exists(input_mesh_file):
                try:
                    representative_projector = DirectProjectionSlicer(
                        mesh_path=input_mesh_file,
                        target_surface_distance=target_bead_width,
                        slice_direction=slicer_slice_direction,
                        inward_normals=True,
                        min_points_req=2
                    )
                    break
                except:
                    continue
        
        if representative_projector is None:
            print("错误: 无法创建代表性切片器实例用于可视化和G-code生成")
        else:
            # 可视化合并后的路径
            # representative_projector.visualize_paths(all_files_paths, 
            #                           show_normals=False, # 设置为True以查看法线
            #                           normal_scale=target_bead_width * 0.5, # 法线可视化长度
            #                           normal_hop_distance=gcode_normal_hop_distance, # 与G-code参数匹配以进行可视化
            #                           clearance_above_model_max_viz=gcode_clearance_above_model_max # 同上
            #                           )
            
            print("\n--- 开始合并后的G-code保存过程 ---")
            gcode_gen_start_time = time.time()
            representative_projector.save_paths_to_gcode(all_files_paths, output_gcode_file_abs, 
                                          klipper_mode=False, # 改为Klipper模式以匹配正常文件的M83相对挤出
                                          feed_rate_print=gcode_print_feedrate,
                                          feed_rate_travel=gcode_travel_feedrate,
                                          target_extruder_temp=gcode_target_extruder_temp,
                                          target_bed_temp=gcode_target_bed_temp,
                                          filament_diameter=gcode_filament_diameter,
                                          extrusion_width=gcode_extrusion_width, 
                                          layer_height=gcode_layer_height,    
                                          retraction_amount=gcode_retraction_amount,
                                          retraction_feedrate=gcode_retraction_feedrate,
                                          extrusion_multiplier=gcode_extrusion_multiplier,
                                          normal_hop_distance=gcode_normal_hop_distance, 
                                          clearance_above_model_max=gcode_clearance_above_model_max, 
                                          rotation_feed_rate=gcode_rotation_feedrate, 
                                          enable_rotation_axes=enable_rotation_axes
                                          ) 
            gcode_gen_duration = time.time() - gcode_gen_start_time
            print(f"--- 合并后的G-code 保存总耗时: {gcode_gen_duration:.4f} 秒 ---")
            print(f"--- 合并后的G-code文件已保存到: {output_gcode_file_abs} ---")
    else:
        print("未能从任何文件生成路径。")
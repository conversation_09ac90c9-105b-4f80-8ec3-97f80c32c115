#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV 间距分析数据可视化工具

本工具用于分析和可视化 direct_projection_slicer.py 生成的间距分析 CSV 文件。
支持以下功能：
- 异常数据检测和移除
- 多维度数据分析
- 可视化图表生成
- 多数据集对比分析

作者: AI Assistant
创建时间: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import glob
import warnings
from scipy import stats
import argparse
import os
import matplotlib

# 抑制所有matplotlib相关的警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
warnings.filterwarnings('ignore', category=matplotlib.MatplotlibDeprecationWarning)
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

# 设置中文字体，如果不可用则使用默认字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    # 如果中文字体不可用，使用英文标签
    pass

class SpacingAnalysisVisualizer:
    """间距分析数据可视化器"""
    
    def __init__(self, csv_files=None, auto_discover=True):
        """
        初始化可视化器
        
        参数:
        csv_files: CSV文件路径列表，如果为None则自动发现
        auto_discover: 是否自动发现项目中的CSV文件
        """
        self.data_dict = {}  # 存储每个文件的数据
        self.cleaned_data_dict = {}  # 存储清理后的数据
        self.outlier_info = {}  # 存储异常值信息
        
        if csv_files is None and auto_discover:
            csv_files = self._discover_csv_files()
        elif csv_files is None:
            csv_files = []
            
        self.load_csv_files(csv_files)
    
    def _discover_csv_files(self):
        """自动发现项目中的间距分析CSV文件"""
        csv_files = []
        
        # 查找当前目录中的CSV文件
        for pattern in ['*spacing*.csv', '*analysis*.csv', '*.csv']:
            files = glob.glob(pattern)
            for file in files:
                # 检查是否是间距分析文件（通过检查列名）
                try:
                    df_test = pd.read_csv(file, nrows=1)
                    required_columns = ['Target3DSpacing_mm', 'ActualAvg3DSpacing_mm', 'Error_mm']
                    if all(col in df_test.columns for col in required_columns):
                        csv_files.append(file)
                        print(f"发现间距分析文件: {file}")
                except:
                    continue
                    
        if not csv_files:
            print("未找到间距分析CSV文件。请手动指定文件路径。")
            
        return csv_files
    
    def load_csv_files(self, csv_files):
        """加载CSV文件"""
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                file_name = Path(file_path).stem
                self.data_dict[file_name] = df
                print(f"成功加载文件: {file_path} ({len(df)} 行数据)")
                
                # 显示基本信息
                print(f"  - 数据范围: 偏移量1 {df['Offset1_mm'].min():.2f} ~ {df['Offset1_mm'].max():.2f} mm")
                print(f"  - 目标间距: {df['Target3DSpacing_mm'].iloc[0]:.3f} mm")
                print(f"  - 实际间距范围: {df['ActualAvg3DSpacing_mm'].min():.3f} ~ {df['ActualAvg3DSpacing_mm'].max():.3f} mm")
                
            except Exception as e:
                print(f"加载文件 {file_path} 失败: {e}")
    
    def detect_outliers(self, method='multiple', z_threshold=3, iqr_factor=1.5):
        """
        检测异常值
        
        参数:
        method: 检测方法 ('z_score', 'iqr', 'isolation', 'multiple')
        z_threshold: Z-score阈值
        iqr_factor: IQR因子
        """
        for file_name, df in self.data_dict.items():
            print(f"\n=== 正在检测 {file_name} 中的异常值 ===")
            
            outlier_mask = np.zeros(len(df), dtype=bool)
            outlier_methods = []
            
            # 主要分析列
            key_columns = ['ActualAvg3DSpacing_mm', 'Error_mm', 'ProjectedStep_mm', 'StdDev3DSpacing_mm']
            
            if method in ['z_score', 'multiple']:
                # Z-score方法
                for col in key_columns:
                    if col in df.columns:
                        z_scores = np.abs(stats.zscore(df[col]))
                        col_outliers = z_scores > z_threshold
                        if col_outliers.any():
                            outlier_mask |= col_outliers
                            outlier_methods.append(f'Z-score_{col}')
                            print(f"Z-score方法在{col}中检测到 {col_outliers.sum()} 个异常值")
            
            if method in ['iqr', 'multiple']:
                # IQR方法
                for col in key_columns:
                    if col in df.columns:
                        Q1 = df[col].quantile(0.25)
                        Q3 = df[col].quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - iqr_factor * IQR
                        upper_bound = Q3 + iqr_factor * IQR
                        col_outliers = (df[col] < lower_bound) | (df[col] > upper_bound)
                        if col_outliers.any():
                            outlier_mask |= col_outliers
                            outlier_methods.append(f'IQR_{col}')
                            print(f"IQR方法在{col}中检测到 {col_outliers.sum()} 个异常值")
            
            if method in ['isolation', 'multiple']:
                # 基于误差的简单异常检测
                error_col = 'Error_mm'
                if error_col in df.columns:
                    # 检测误差绝对值过大的点
                    error_threshold = df[error_col].std() * 2.5
                    error_outliers = np.abs(df[error_col]) > error_threshold
                    if error_outliers.any():
                        outlier_mask |= error_outliers
                        outlier_methods.append('Error_threshold')
                        print(f"误差阈值方法检测到 {error_outliers.sum()} 个异常值")
            
            # 存储异常值信息
            self.outlier_info[file_name] = {
                'mask': outlier_mask,
                'methods': outlier_methods,
                'count': outlier_mask.sum(),
                'percentage': (outlier_mask.sum() / len(df)) * 100
            }
            
            print(f"检测到的异常值总数: {outlier_mask.sum()} ({(outlier_mask.sum()/len(df)*100):.1f}%)")
            
            # 创建清理后的数据
            cleaned_df = df[~outlier_mask].copy()
            self.cleaned_data_dict[file_name] = cleaned_df
            print(f"清理后的数据: {len(cleaned_df)} 行")
    
    def visualize_overview(self, use_cleaned=True, save_plots=False):
        """生成概览图表"""
        data_dict = self.cleaned_data_dict if use_cleaned else self.data_dict
        
        for file_name, df in data_dict.items():
            print(f"\n=== 正在生成 {file_name} 的概览图表 ===")
            
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle(f'间距分析概览 - {file_name}', fontsize=16, fontweight='bold')
            
            # 1. 实际间距 vs 目标间距
            ax1 = axes[0, 0]
            ax1.plot(df.index, df['ActualAvg3DSpacing_mm'], 'b-', linewidth=2, label='实际平均间距')
            ax1.axhline(y=df['Target3DSpacing_mm'].iloc[0], color='r', linestyle='--', 
                       linewidth=2, label='目标间距')
            ax1.set_xlabel('数据点索引')
            ax1.set_ylabel('间距 (mm)')
            ax1.set_title('实际间距与目标间距对比')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 误差分布
            ax2 = axes[0, 1]
            ax2.plot(df.index, df['Error_mm'], 'g-', linewidth=2)
            ax2.axhline(y=0, color='r', linestyle='--', alpha=0.5)
            ax2.set_xlabel('数据点索引')
            ax2.set_ylabel('误差 (mm)')
            ax2.set_title('间距误差变化')
            ax2.grid(True, alpha=0.3)
            
            # 3. 投影步长变化
            ax3 = axes[0, 2]
            ax3.plot(df.index, df['ProjectedStep_mm'], 'm-', linewidth=2)
            ax3.set_xlabel('数据点索引')
            ax3.set_ylabel('投影步长 (mm)')
            ax3.set_title('投影步长变化')
            ax3.grid(True, alpha=0.3)
            
            # 4. 误差直方图
            ax4 = axes[1, 0]
            ax4.hist(df['Error_mm'], bins=30, alpha=0.7, color='lightblue', edgecolor='black')
            ax4.axvline(x=0, color='r', linestyle='--', linewidth=2)
            ax4.set_xlabel('误差 (mm)')
            ax4.set_ylabel('频率')
            ax4.set_title('误差分布直方图')
            ax4.grid(True, alpha=0.3)
            
            # 5. 间距散点图
            ax5 = axes[1, 1]
            scatter = ax5.scatter(df['Offset1_mm'], df['ActualAvg3DSpacing_mm'], 
                                c=df['Error_mm'], cmap='RdYlBu_r', alpha=0.7)
            ax5.set_xlabel('起始偏移量 (mm)')
            ax5.set_ylabel('实际间距 (mm)')
            ax5.set_title('间距与位置关系 (颜色=误差)')
            plt.colorbar(scatter, ax=ax5, label='误差 (mm)')
            
            # 6. 统计摘要
            ax6 = axes[1, 2]
            ax6.axis('off')
            
            # 计算统计信息
            stats_text = f"""
统计摘要:
数据点数: {len(df)}
目标间距: {df['Target3DSpacing_mm'].iloc[0]:.3f} mm

实际间距:
  平均值: {df['ActualAvg3DSpacing_mm'].mean():.3f} mm
  标准差: {df['ActualAvg3DSpacing_mm'].std():.3f} mm
  最小值: {df['ActualAvg3DSpacing_mm'].min():.3f} mm
  最大值: {df['ActualAvg3DSpacing_mm'].max():.3f} mm

误差:
  平均值: {df['Error_mm'].mean():.3f} mm
  标准差: {df['Error_mm'].std():.3f} mm
  平均绝对误差: {df['Error_mm'].abs().mean():.3f} mm

投影步长:
  平均值: {df['ProjectedStep_mm'].mean():.3f} mm
  标准差: {df['ProjectedStep_mm'].std():.3f} mm
            """
            
            ax6.text(0.1, 0.9, stats_text, transform=ax6.transAxes, fontsize=10,
                    verticalalignment='top', fontfamily='monospace',
                    bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.5))
            
            plt.tight_layout()
            
            if save_plots:
                filename = f'spacing_analysis_overview_{file_name}.png'
                plt.savefig(filename, dpi=300, bbox_inches='tight')
                print(f"概览图表已保存: {filename}")
            
            plt.show()
    
    def visualize_detailed_analysis(self, use_cleaned=True, save_plots=False):
        """生成详细分析图表"""
        data_dict = self.cleaned_data_dict if use_cleaned else self.data_dict
        
        for file_name, df in data_dict.items():
            print(f"\n=== 正在生成 {file_name} 的详细分析 ===")
            
            # 创建多个子图
            fig = plt.figure(figsize=(20, 16))
            
            # 1. 时间序列分析
            ax1 = plt.subplot(3, 3, 1)
            plt.plot(df['Offset1_mm'], df['ActualAvg3DSpacing_mm'], 'b-', linewidth=1.5, alpha=0.8)
            plt.axhline(y=df['Target3DSpacing_mm'].iloc[0], color='r', linestyle='--', linewidth=2)
            plt.xlabel('起始偏移量 (mm)')
            plt.ylabel('实际间距 (mm)')
            plt.title('间距与位置关系')
            plt.grid(True, alpha=0.3)
            
            # 2. 误差分析
            ax2 = plt.subplot(3, 3, 2)
            plt.plot(df['Offset1_mm'], df['Error_mm'], 'r-', linewidth=1.5, alpha=0.8)
            plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
            plt.xlabel('起始偏移量 (mm)')
            plt.ylabel('误差 (mm)')
            plt.title('误差与位置关系')
            plt.grid(True, alpha=0.3)
            
            # 3. 箱线图比较
            ax3 = plt.subplot(3, 3, 3)
            box_data = [df['Target3DSpacing_mm'], df['ActualAvg3DSpacing_mm'], 
                       df['Min3DSpacing_mm'], df['Max3DSpacing_mm']]
            box_labels = ['目标间距', '实际平均', '最小值', '最大值']
            plt.boxplot(box_data, tick_labels=box_labels)
            plt.ylabel('间距 (mm)')
            plt.title('间距分布对比')
            plt.xticks(rotation=45)
            
            # 4. 相关性热图
            ax4 = plt.subplot(3, 3, 4)
            corr_columns = ['ProjectedStep_mm', 'ActualAvg3DSpacing_mm', 'Error_mm', 
                           'Min3DSpacing_mm', 'Max3DSpacing_mm', 'StdDev3DSpacing_mm']
            corr_data = df[corr_columns].corr()
            sns.heatmap(corr_data, annot=True, cmap='RdYlBu_r', center=0, 
                       square=True, cbar_kws={'shrink': 0.8})
            plt.title('变量相关性')
            
            # 5. 误差分布密度图
            ax5 = plt.subplot(3, 3, 5)
            plt.hist(df['Error_mm'], bins=50, density=True, alpha=0.7, color='lightblue')
            # 添加正态分布拟合
            mu, sigma = stats.norm.fit(df['Error_mm'])
            x = np.linspace(df['Error_mm'].min(), df['Error_mm'].max(), 100)
            plt.plot(x, stats.norm.pdf(x, mu, sigma), 'r-', linewidth=2, 
                    label=f'正态分布拟合 (μ={mu:.3f}, σ={sigma:.3f})')
            plt.xlabel('误差 (mm)')
            plt.ylabel('密度')
            plt.title('误差分布密度')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # 6. Q-Q图检验正态性
            ax6 = plt.subplot(3, 3, 6)
            stats.probplot(df['Error_mm'], dist="norm", plot=plt)
            plt.title('误差正态性Q-Q图')
            plt.grid(True, alpha=0.3)
            
            # 7. 移动窗口统计
            ax7 = plt.subplot(3, 3, 7)
            window_size = max(5, len(df) // 20)  # 动态窗口大小
            rolling_mean = df['Error_mm'].rolling(window=window_size).mean()
            rolling_std = df['Error_mm'].rolling(window=window_size).std()
            plt.plot(df.index, rolling_mean, 'b-', linewidth=2, label=f'移动平均 (窗口={window_size})')
            plt.fill_between(df.index, rolling_mean - rolling_std, rolling_mean + rolling_std, 
                           alpha=0.3, label='±1 标准差')
            plt.xlabel('数据点索引')
            plt.ylabel('误差 (mm)')
            plt.title('误差移动统计')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # 8. 散点图矩阵的简化版
            ax8 = plt.subplot(3, 3, 8)
            plt.scatter(df['ProjectedStep_mm'], df['ActualAvg3DSpacing_mm'], 
                       c=df['Error_mm'], cmap='RdYlBu_r', alpha=0.6)
            plt.xlabel('投影步长 (mm)')
            plt.ylabel('实际间距 (mm)')
            plt.title('步长与间距关系 (颜色=误差)')
            plt.colorbar(label='误差 (mm)')
            
            # 9. 性能指标
            ax9 = plt.subplot(3, 3, 9)
            ax9.axis('off')
            
            # 计算性能指标
            mae = df['Error_mm'].abs().mean()
            rmse = np.sqrt((df['Error_mm'] ** 2).mean())
            mape = (df['Error_mm'].abs() / df['Target3DSpacing_mm']).mean() * 100
            try:
                r2 = stats.pearsonr(df['Target3DSpacing_mm'], df['ActualAvg3DSpacing_mm'])[0] ** 2
            except:
                r2 = 0.0  # 处理常数输入警告
            
            performance_text = f"""
性能指标:

平均绝对误差 (MAE):
{mae:.4f} mm

均方根误差 (RMSE):
{rmse:.4f} mm

平均绝对百分比误差 (MAPE):
{mape:.2f}%

决定系数 (R²):
{r2:.4f}

数据质量:
有效样本数: {len(df)}
平均采样点数: {df['NumSamples'].mean():.1f}
标准差范围: {df['StdDev3DSpacing_mm'].min():.4f} - {df['StdDev3DSpacing_mm'].max():.4f}
            """
            
            ax9.text(0.1, 0.9, performance_text, transform=ax9.transAxes, fontsize=10,
                    verticalalignment='top', fontfamily='monospace',
                    bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.3))
            
            plt.suptitle(f'详细分析 - {file_name}', fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            if save_plots:
                filename = f'spacing_analysis_detailed_{file_name}.png'
                plt.savefig(filename, dpi=300, bbox_inches='tight')
                print(f"详细分析图表已保存: {filename}")
            
            plt.show()
    
    def compare_datasets(self, save_plots=False):
        """比较多个数据集"""
        if len(self.cleaned_data_dict) < 2:
            print("需要至少两个数据集进行比较")
            return
        
        print(f"\n=== 正在比较 {len(self.cleaned_data_dict)} 个数据集 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('数据集对比分析', fontsize=16, fontweight='bold')
        
        # 准备比较数据
        comparison_data = {}
        for name, df in self.cleaned_data_dict.items():
            comparison_data[name] = {
                'MAE': df['Error_mm'].abs().mean(),
                'RMSE': np.sqrt((df['Error_mm'] ** 2).mean()),
                'Mean_Actual': df['ActualAvg3DSpacing_mm'].mean(),
                'Std_Actual': df['ActualAvg3DSpacing_mm'].std(),
                'Target': df['Target3DSpacing_mm'].iloc[0]
            }
        
        # 1. 误差比较
        ax1 = axes[0, 0]
        names = list(comparison_data.keys())
        mae_values = [comparison_data[name]['MAE'] for name in names]
        rmse_values = [comparison_data[name]['RMSE'] for name in names]
        
        x = np.arange(len(names))
        width = 0.35
        ax1.bar(x - width/2, mae_values, width, label='MAE', alpha=0.8)
        ax1.bar(x + width/2, rmse_values, width, label='RMSE', alpha=0.8)
        ax1.set_xlabel('数据集')
        ax1.set_ylabel('误差 (mm)')
        ax1.set_title('误差指标对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(names, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 实际间距比较
        ax2 = axes[0, 1]
        for name, df in self.cleaned_data_dict.items():
            ax2.plot(df.index / len(df), df['ActualAvg3DSpacing_mm'], 
                    label=name, linewidth=2, alpha=0.8)
        ax2.set_xlabel('归一化位置')
        ax2.set_ylabel('实际间距 (mm)')
        ax2.set_title('实际间距对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 误差分布比较
        ax3 = axes[1, 0]
        error_data = [df['Error_mm'] for df in self.cleaned_data_dict.values()]
        ax3.boxplot(error_data, tick_labels=list(self.cleaned_data_dict.keys()))
        ax3.set_ylabel('误差 (mm)')
        ax3.set_title('误差分布对比')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. 统计摘要表
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        summary_text = "数据集统计摘要:\n\n"
        for name, stats in comparison_data.items():
            summary_text += f"{name}:\n"
            summary_text += f"  目标间距: {stats['Target']:.3f} mm\n"
            summary_text += f"  实际平均值: {stats['Mean_Actual']:.3f} mm\n"
            summary_text += f"  标准差: {stats['Std_Actual']:.3f} mm\n"
            summary_text += f"  MAE: {stats['MAE']:.4f} mm\n"
            summary_text += f"  RMSE: {stats['RMSE']:.4f} mm\n\n"
        
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, fontsize=9,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.3))
        
        plt.tight_layout()
        
        if save_plots:
            filename = 'spacing_analysis_comparison.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"对比分析图表已保存: {filename}")
        
        plt.show()
    
    def save_cleaned_data(self):
        """保存清理后的数据"""
        for file_name, df in self.cleaned_data_dict.items():
            # output_file = f"{file_name}_cleaned.csv"
            # df.to_csv(output_file, index=False)
            # print(f"Cleaned data saved: {output_file}")
            pass # 现在不保存cleaned.csv文件
            
            # 也保存异常值报告 (这部分已经被注释掉了)
            # if file_name in self.outlier_info:
            #     outlier_report = f"{file_name}_outlier_report.txt"
            #     with open(outlier_report, 'w', encoding='utf-8') as f:
            #         info = self.outlier_info[file_name]
            #         f.write(f"Outlier Detection Report - {file_name}\\n")
            #         f.write(f"{'='*50}\\n\\n")
            #         f.write(f"Original data points: {len(self.data_dict[file_name])}\\n")
            #         f.write(f"Detected outliers: {info['count']}\\n")
            #         f.write(f"Outlier percentage: {info['percentage']:.2f}%\\n")
            #         f.write(f"Detection methods: {', '.join(info['methods'])}\\n")
            #         f.write(f"Cleaned data points: {len(df)}\\n\\n")
            #         
            #         # 添加异常值索引
            #         if info['count'] > 0:
            #             outlier_indices = np.where(info['mask'])[0]
            #             f.write(f"Outlier indices: {list(outlier_indices)}\\n")
            #     
            #     print(f"Outlier report saved: {outlier_report}")
    
    def generate_report(self):
        """生成完整的分析报告"""
        print("\n" + "="*60)
        print("间距分析数据可视化报告")
        print("="*60)
        
        print(f"\n加载的数据集数量: {len(self.data_dict)}")
        
        for file_name in self.data_dict.keys():
            print(f"\n--- {file_name} ---")
            original_df = self.data_dict[file_name]
            cleaned_df = self.cleaned_data_dict.get(file_name, original_df)
            
            print(f"原始数据点数: {len(original_df)}")
            if file_name in self.outlier_info:
                outlier_count = self.outlier_info[file_name]['count']
                print(f"异常值数量: {outlier_count} ({outlier_count/len(original_df)*100:.1f}%)")
            print(f"清理后数据点数: {len(cleaned_df)}")
            
            print(f"\n目标间距: {cleaned_df['Target3DSpacing_mm'].iloc[0]:.3f} mm")
            print(f"实际间距统计:")
            print(f"   平均值: {cleaned_df['ActualAvg3DSpacing_mm'].mean():.4f} mm")
            print(f"   标准差: {cleaned_df['ActualAvg3DSpacing_mm'].std():.4f} mm")
            print(f"   范围: {cleaned_df['ActualAvg3DSpacing_mm'].min():.4f} ~ {cleaned_df['ActualAvg3DSpacing_mm'].max():.4f} mm")
            
            print(f"\n误差统计:")
            print(f"   平均误差: {cleaned_df['Error_mm'].mean():.4f} mm")
            print(f"   平均绝对误差: {cleaned_df['Error_mm'].abs().mean():.4f} mm")
            print(f"   均方根误差: {np.sqrt((cleaned_df['Error_mm'] ** 2).mean()):.4f} mm")
            print(f"   误差范围: {cleaned_df['Error_mm'].min():.4f} ~ {cleaned_df['Error_mm'].max():.4f} mm")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='间距分析CSV数据可视化工具')
    parser.add_argument('--files', '-f', nargs='+', help='指定CSV文件路径')
    parser.add_argument('--no-auto', action='store_true', help='禁用自动发现CSV文件')
    parser.add_argument('--method', '-m', default='multiple', 
                       choices=['z_score', 'iqr', 'isolation', 'multiple'],
                       help='异常值检测方法')
    parser.add_argument('--save', action='store_true', help='保存生成的图表')
    parser.add_argument('--no-clean', action='store_true', help='不移除异常值')
    
    args = parser.parse_args()
    
    # 创建可视化器
    visualizer = SpacingAnalysisVisualizer(
        csv_files=args.files, 
        auto_discover=not args.no_auto
    )
    
    if not visualizer.data_dict:
        print("错误：未找到数据文件")
        return
    
    # 检测异常值
    if not args.no_clean:
        visualizer.detect_outliers(method=args.method)
    else:
        # 如果不清理，将原始数据复制到cleaned_data_dict
        visualizer.cleaned_data_dict = visualizer.data_dict.copy()
    
    # 生成可视化
    save_plots = args.save
    
    visualizer.visualize_overview(use_cleaned=not args.no_clean, save_plots=save_plots)
    visualizer.visualize_detailed_analysis(use_cleaned=not args.no_clean, save_plots=save_plots)
    
    if len(visualizer.cleaned_data_dict) > 1:
        visualizer.compare_datasets(save_plots=save_plots)
    
    # 保存清理后的数据
    if not args.no_clean:
        visualizer.save_cleaned_data()
    
    # 生成报告
    visualizer.generate_report()
    
    print("\n分析完成！")


if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示改进的投影可视化功能
展示将2D填充路径放置在模型上方10mm处，然后用虚线连接到3D投影点的效果
"""

import os
import sys
from direct_projection_slicer import DirectProjectionSlicer

def demo_projection_visualization():
    """演示改进的投影可视化功能"""
    
    print("=== 投影可视化功能演示 ===")
    print("功能特点：")
    print("1. 左侧显示3D路径（边界和填充）")
    print("2. 右侧显示2D填充规划")
    print("3. 2D填充点位于模型上方10mm处")
    print("4. 红色虚线显示从2D到3D的投影关系")
    print("5. 半透明青色平面表示2D规划层")
    print()
    
    # 检查STL文件是否存在
    stl_file = "src/m19_mnp_001.stl"
    if not os.path.exists(stl_file):
        print(f"错误: STL文件 {stl_file} 不存在")
        print("请确保有一个STL文件在当前目录中用于演示")
        return
    
    print(f"使用STL文件: {stl_file}")
    
    try:
        # 创建切片器实例
        projector = DirectProjectionSlicer(
            mesh_path='src/m19_mnp_001.stl',
            target_surface_distance=0.4,  # 目标表面距离
            slice_direction='x',          # 切片方向
            inward_normals=True,          # 法线朝内
            min_points_req=2              # 路径段所需最小点数
        )
        
        print("✓ 切片器初始化成功")
        
        # 设置参数
        row_spacing = 0.8      # 行间距(mm)
        offset_distance = 0.5  # 距离边界的偏移(mm)
        max_segment_length = 0.5  # 最大段长度(mm)
        
        print(f"\n参数设置:")
        print(f"  行间距: {row_spacing}mm")
        print(f"  边界偏移: {offset_distance}mm")
        print(f"  最大段长: {max_segment_length}mm")
        
        # 使用投影策略生成路径（这将自动显示组合可视化）
        print("\n--- 使用投影策略生成路径并显示增强的可视化 ---")
        paths_data, spacing_data = projector.create_projected_fill_paths(
            row_spacing=row_spacing,
            offset_distance=offset_distance,
            max_segment_length=max_segment_length,
            strategy='projection',              # 使用投影策略
            projection_fill_pattern='raster',   # 光栅填充模式
            proximity_threshold=0.05,           # 边界距离阈值
            adaptive_density=False              # 投影策略不使用自适应密度
        )
        
        if paths_data:
            print(f"✓ 成功生成 {len(paths_data)} 个路径段")
            print("\n可视化窗口说明：")
            print("• 左侧3D视图:")
            print("  - 绿色线条：边界路径")
            print("  - 蓝色线条：填充路径")
            print("  - 紫色点：2D填充点（位于上方10mm）")
            print("  - 洋红色线：2D填充路径连接")
            print("  - 红色虚线：投影连接线")
            print("  - 青色半透明面：2D规划平面")
            print("• 右侧2D视图:")
            print("  - 蓝色实线：原始边界轮廓")
            print("  - 绿色虚线：内缩后填充区域")
            print("  - 紫色点：路径点")
            print("  - 蓝色/红色线：实际填充路径/虚拟连接")
            print("\n✓ 增强的投影可视化已显示！")
        else:
            print("✗ 未能生成任何路径段")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_projection_visualization() 
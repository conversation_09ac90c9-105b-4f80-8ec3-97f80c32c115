import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import os
import trimesh # trimesh is needed for loading meshes and for mesh properties.

# Attempt to import the slicer; assumes visualizations.py is in the same directory or slicer is in PYTHONPATH
try:
    from direct_projection_slicer import DirectProjectionSlicer
except ImportError:
    print("Error: Could not import DirectProjectionSlicer. Ensure it's in the same directory or PYTHONPATH.")
    # As a fallback for environments where direct import might fail during generation,
    # define a placeholder if the class is not found.
    # This allows the rest of the script structure to be defined,
    # but it won't run without the actual class.
    class DirectProjectionSlicer:
        def __init__(self, mesh_path, **kwargs):
            print(f"Placeholder: Tried to load {mesh_path} with DirectProjectionSlicer.")
            self.mesh = trimesh.creation.box() # Placeholder mesh
            self.mesh_bounds = self.mesh.bounds
            self.axis_index = 0 # Default
            self.d_surf = 0.4
            self.min_points_req = 2
            self.inward_normals = True
            print("Warning: Using placeholder DirectProjectionSlicer. Functionality will be limited.")
        
        def load_mesh(self, mesh_path):
            if os.path.exists(mesh_path):
                return trimesh.load_mesh(mesh_path)
            else:
                print(f"Warning: Mesh file {mesh_path} not found. Using a default box.")
                return trimesh.creation.box()

        def create_projected_fill_paths(self, **kwargs):
            print("Placeholder: create_projected_fill_paths called.")
            # Return plausible dummy data structure
            # paths_list: list of (points_3d, normals_3d, is_boundary, segment_id)
            # spacing_data: list of dicts
            dummy_points = np.array([[0,0,0],[1,0,0],[1,1,0]])
            dummy_normals = np.array([[0,0,1],[0,0,1],[0,0,1]])
            return [(dummy_points, dummy_normals, False, -10000)], [{'Offset1_mm': 0, 'Offset2_mm': 1, 'ProjectedStep_mm':1, 'Target3DSpacing_mm':1, 'ActualAvg3DSpacing_mm':1}]

        def get_surface_normals_batch(self, points):
             return np.tile([0,0,1], (len(points),1))

        def visualize_paths(self, paths_data, show_normals=False, normal_scale=0.5,
                            normal_hop_distance=None, clearance_above_model_max_viz=None,
                            show_2d_fill=False, fill_2d_data=None, projection_connections=None):
            print(f"Placeholder: visualize_paths called. normal_hop_distance={normal_hop_distance}, clearance_above_model_max_viz={clearance_above_model_max_viz}")
            # This placeholder will now create its own figure and axes internally, like the actual class
            fig = plt.figure(figsize=(10,8))
            ax = fig.add_subplot(111, projection='3d')
            
            if self.mesh:
                 ax.plot_trisurf(self.mesh.vertices[:,0], self.mesh.vertices[:,1], self.mesh.vertices[:,2], triangles=self.mesh.faces, alpha=0.1, color='gray')

            # Simplified plotting of paths_data
            for points, _, is_boundary, _ in paths_data:
                if points is not None and len(points) > 0:
                    color = 'g' if is_boundary else 'b'
                    ax.plot(points[:,0], points[:,1], points[:,2], color=color, marker='.')
            
            # Simplified travel move plotting for placeholder
            if normal_hop_distance is not None and clearance_above_model_max_viz is not None and len(paths_data) > 1:
                for i in range(len(paths_data) -1):
                    p_curr_end = paths_data[i][0][-1]
                    p_next_start = paths_data[i+1][0][0]
                    
                    # Simplified hop: up, over, down
                    p_hop1 = p_curr_end + np.array([0,0,normal_hop_distance])
                    z_safe = self.mesh.bounds[1,2] + clearance_above_model_max_viz
                    p_hop2 = np.array([p_hop1[0], p_hop1[1], z_safe])
                    p_hop3 = np.array([p_next_start[0], p_next_start[1], z_safe])
                    p_hop4 = p_next_start + np.array([0,0,normal_hop_distance]) # Pre-approach above next_start
                    
                    travel_path = np.array([p_curr_end, p_hop1, p_hop2, p_hop3, p_hop4, p_next_start])
                    ax.plot(travel_path[:,0], travel_path[:,1], travel_path[:,2], color='r', linestyle=':')
            
            ax.set_xlabel("X (mm)")
            ax.set_ylabel("Y (mm)")
            ax.set_zlabel("Z (mm)")
            
            # Calculate overall bounds for all plotted data if possible
            all_points_list = [self.mesh.vertices.copy()]
            if paths_data:
                for points, _, _, _ in paths_data:
                    if points is not None and len(points) > 0:
                        all_points_list.append(points)
            
            if len(all_points_list) > 1:
                all_points_combined = np.vstack(all_points_list)
                min_vals, max_vals = np.min(all_points_combined, axis=0), np.max(all_points_combined, axis=0)
            elif len(all_points_list) == 1: # Only mesh vertices
                min_vals, max_vals = np.min(all_points_list[0], axis=0), np.max(all_points_list[0], axis=0)
            else: # Fallback to mesh bounds if no paths_data or paths_data is empty
                min_vals, max_vals = self.mesh.bounds

            center = (min_vals + max_vals) / 2.0
            ranges = max_vals - min_vals
            max_range = np.max(ranges)
            if max_range < 1e-6: # Avoid division by zero or tiny ranges
                max_range = 1.0 # Default to a unit cube range if data is a point or very small

            ax.set_xlim(center[0] - max_range / 2, center[0] + max_range / 2)
            ax.set_ylim(center[1] - max_range / 2, center[1] + max_range / 2)
            ax.set_zlim(center[2] - max_range / 2, center[2] + max_range / 2)
            # ax.set_box_aspect([1,1,1]) # This can be used if preferred over manual limits

            ax.legend(loc='upper left', bbox_to_anchor=(1,1))
            fig.tight_layout(rect=[0,0,0.85,1])
            
            caption = f"""
            图注:
            - 灰色表面: 原始3D网格模型。
            - 蓝色路径: 由ADOS策略在曲面上生成的3D打印路径条带。
            - 红色虚线 (如果显示): 代表在偏置轴 ({'Y' if self.axis_index == 1 else 'X'}) 上的切片位置。
              ADOS通过迭代调整这些切片位置（使其可能非均匀分布），
              以在3D曲面上实现更均匀的路径间距。
            """
            fig.text(0.02, 0.02, caption, transform=fig.transFigure, va="bottom", ha="left", fontsize=8, wrap=True)
            plt.show()


def plot_ados_principle(slicer, paths_data, spacing_analysis_data, offset_dir_axis, target_3d_spacing_objective):
    """
    图X：可视化ADOS策略核心原理。
    显示网格、最终的3D路径条带，以及（如果可能）代表自适应切片位置的线。
    """
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 1. Plot the mesh (wireframe only, ensuring legend compatibility)
    if slicer.mesh:
        ax.plot_trisurf(slicer.mesh.vertices[:,0], slicer.mesh.vertices[:,1], slicer.mesh.vertices[:,2], 
                        triangles=slicer.mesh.faces, 
                        facecolor=(0,0,0,0), # Use an actual transparent color tuple, not 'none' for legend stability
                        edgecolor='black', # Color for mesh lines
                        linewidth=0.5, # Thickness of mesh lines
                        label='原始网格 (仅线框)')

    # 2. Plot the generated 3D path strips (fill segments)
    num_fill_segments = 0
    if paths_data:
        for points, normals, is_boundary, segment_id in paths_data:
            if not is_boundary and points is not None and len(points) >= slicer.min_points_req:
                ax.plot(points[:,0], points[:,1], points[:,2], color='blue', linewidth=2, label='ADOS生成路径' if num_fill_segments == 0 else None)
                num_fill_segments +=1
    
    # 3. Visualize slice plane positions (if spacing_analysis_data is informative)
    if spacing_analysis_data:
        slice_positions = sorted(list(set([d['Offset1_mm'] for d in spacing_analysis_data] + [d['Offset2_mm'] for d in spacing_analysis_data])))
        
        min_bounds, max_bounds = slicer.mesh.bounds
        scan_axis = 1 - offset_dir_axis # This is the axis along which the plane extends (X or Y)
        z_min, z_max = min_bounds[2], max_bounds[2]
        scan_min, scan_max = min_bounds[scan_axis], max_bounds[scan_axis]
        
        # Create a meshgrid for the plane surface (used by plot_surface)
        scan_coords_for_surface = np.linspace(scan_min, scan_max, 2)
        z_coords_for_surface = np.linspace(z_min, z_max, 2)

        first_slice_plane = True # For legend label of slice planes
        first_intersection_points = True # For legend label of intersection points
        for slice_coord_val in slice_positions:
            # Define plane for plot_surface and for trimesh intersection
            if offset_dir_axis == 1: # Y is offset axis, X is scan axis. Plane is XZ at Y=slice_coord_val
                X_surf, Z_surf = np.meshgrid(scan_coords_for_surface, z_coords_for_surface)
                Y_surf = np.full_like(X_surf, slice_coord_val)
                ax.plot_surface(X_surf, Y_surf, Z_surf, color='red', alpha=0.05, linewidth=0, shade=False, 
                                label='切片平面 (非均匀)' if first_slice_plane else None)
                plane_normal = [0, 1, 0]
                plane_origin = [0, slice_coord_val, 0]
            elif offset_dir_axis == 0: # X is offset axis, Y is scan axis. Plane is YZ at X=slice_coord_val
                Y_surf, Z_surf = np.meshgrid(scan_coords_for_surface, z_coords_for_surface)
                X_surf = np.full_like(Y_surf, slice_coord_val)
                ax.plot_surface(X_surf, Y_surf, Z_surf, color='red', alpha=0.05, linewidth=0, shade=False,
                                label='切片平面 (非均匀)' if first_slice_plane else None)
                plane_normal = [1, 0, 0]
                plane_origin = [slice_coord_val, 0, 0]
            first_slice_plane = False

            # Calculate and plot intersections with the mesh
            if slicer.mesh:
                try:
                    # section method returns a Path3D object (or list of Path3D if disjoint sections)
                    # For simple planar sections, usually a single Path3D or None.
                    intersection_path3d = slicer.mesh.section(plane_origin=plane_origin, plane_normal=plane_normal)
                    
                    if intersection_path3d is not None:
                        # The result could be a single Path3D or a list of Path3D objects
                        # If it's a single Path3D, its vertices are directly accessible
                        # If it's a list, iterate through them (though for a plane, often one or none)
                        paths_to_plot = []
                        if isinstance(intersection_path3d, trimesh.path.path.Path3D):
                            paths_to_plot.append(intersection_path3d)
                        elif isinstance(intersection_path3d, list):
                            paths_to_plot.extend(intersection_path3d)
                        
                        for path_segment in paths_to_plot:
                            if path_segment.vertices is not None and len(path_segment.vertices) > 0:
                                ax.scatter(path_segment.vertices[:,0], path_segment.vertices[:,1], path_segment.vertices[:,2], 
                                           color='magenta', s=10, marker='o', 
                                           label='网格与切面交点' if first_intersection_points else None)
                                first_intersection_points = False # Only label once
                except Exception as e:
                    print(f"Error calculating mesh section for slice {slice_coord_val}: {e}")

    ax.set_title("图X：自适应直接三维偏置切层（ADOS）策略核心原理示意")
    # ax.set_xlabel("X (mm)") # Removed as per user request
    # ax.set_ylabel("Y (mm)") # Removed as per user request
    # ax.set_zlabel("Z (mm)") # Removed as per user request
    
    # Turn off the entire axis frame, ticks, labels, and panes
    ax.set_axis_off()

    # Calculate overall bounds for all plotted data if possible
    all_points_list = [slicer.mesh.vertices.copy()]
    if paths_data:
        for points, _, _, _ in paths_data:
            if points is not None and len(points) > 0:
                all_points_list.append(points)
    
    if len(all_points_list) > 1:
        all_points_combined = np.vstack(all_points_list)
        min_vals, max_vals = np.min(all_points_combined, axis=0), np.max(all_points_combined, axis=0)
    elif len(all_points_list) == 1: # Only mesh vertices
        min_vals, max_vals = np.min(all_points_list[0], axis=0), np.max(all_points_list[0], axis=0)
    else: # Fallback to mesh bounds if no paths_data or paths_data is empty
        min_vals, max_vals = slicer.mesh.bounds

    center = (min_vals + max_vals) / 2.0
    ranges = max_vals - min_vals
    max_range = np.max(ranges)
    if max_range < 1e-6:
        max_range = 1.0

    ax.set_xlim(center[0] - max_range / 2, center[0] + max_range / 2)
    ax.set_ylim(center[1] - max_range / 2, center[1] + max_range / 2)
    ax.set_zlim(center[2] - max_range / 2, center[2] + max_range / 2)
    # ax.set_box_aspect([1,1,1])

    ax.legend(loc='upper left', bbox_to_anchor=(1,1))
    fig.tight_layout(rect=[0,0,0.85,1])
    
    caption = f"""
    图注:
    - 黑色线框: 原始3D网格模型 (仅线框)。
    - 蓝色路径: 由ADOS策略在曲面上生成的3D打印路径条带。
    - 红色半透明平面 (如果显示): 代表在偏置轴 ({'Y' if offset_dir_axis == 1 else 'X'}) 上的切片平面。
    - 洋红色点 (如果显示): 原始网格与切片平面的交点。
      ADOS通过迭代调整这些切片位置（使其可能非均匀分布），
      以在3D曲面上实现更均匀的路径间距 (目标间距: {target_3d_spacing_objective:.2f}mm)。
    """
    fig.text(0.02, 0.02, caption, transform=fig.transFigure, va="bottom", ha="left", fontsize=8, wrap=True)
    plt.show()


def plot_snake_connection_logic(slicer, connected_paths_data):
    """
    图Y：可视化智能蛇形连接算法的决策机制（结果）。
    显示网格和最终连接的路径。重点说明连接的条件。
    """
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')

    if slicer.mesh:
        ax.plot_trisurf(slicer.mesh.vertices[:,0], slicer.mesh.vertices[:,1], slicer.mesh.vertices[:,2], 
                        triangles=slicer.mesh.faces, alpha=0.1, color='lightgray', label='原始网格')

    # Plot connected paths.
    # It's hard to show pre-connection state easily without modifying the slicer.
    # So, we plot the result and explain the logic.
    first_fill_path = True
    if connected_paths_data:
        for points, normals, is_boundary, segment_id in connected_paths_data:
            if not is_boundary and points is not None and len(points) >= slicer.min_points_req:
                # Negative segment_ids from _create_direct_offset_paths might indicate
                # distinct snake-connected components. Use this to vary color for illustration.
                color_val = abs(segment_id % 10) / 10.0 # Simple hash for color
                path_color = plt.cm.viridis(color_val)
                
                ax.plot(points[:,0], points[:,1], points[:,2], color=path_color, linewidth=1.5,
                        label="连接后的填充路径段" if first_fill_path else None)
                first_fill_path = False
                
                # Illustrate a connection point (e.g., start of a segment if it's not the very first)
                # This is highly illustrative as we don't know the exact connection logic from here.
                if segment_id < -10000 + 100 : # Not the very first segment ID from a file potentially
                    # Highlight the start of this *possibly* connected segment
                    ax.scatter(points[0,0], points[0,1], points[0,2], color='red', s=20, marker='o', label="连接点 (示意)" if first_fill_path else None)


    ax.set_title("图Y：智能蛇形连接算法决策机制示意")
    ax.set_xlabel("X (mm)")
    ax.set_ylabel("Y (mm)")
    ax.set_zlabel("Z (mm)")
    
    # Calculate overall bounds for all plotted data if possible
    all_points_list = [slicer.mesh.vertices.copy()]
    if connected_paths_data:
        for points, _, _, _ in connected_paths_data:
            if points is not None and len(points) > 0:
                all_points_list.append(points)

    if len(all_points_list) > 1:
        all_points_combined = np.vstack(all_points_list)
        min_vals, max_vals = np.min(all_points_combined, axis=0), np.max(all_points_combined, axis=0)
    elif len(all_points_list) == 1:
        min_vals, max_vals = np.min(all_points_list[0], axis=0), np.max(all_points_list[0], axis=0)
    else:
        min_vals, max_vals = slicer.mesh.bounds

    center = (min_vals + max_vals) / 2.0
    ranges = max_vals - min_vals
    max_range = np.max(ranges)
    if max_range < 1e-6:
        max_range = 1.0

    ax.set_xlim(center[0] - max_range / 2, center[0] + max_range / 2)
    ax.set_ylim(center[1] - max_range / 2, center[1] + max_range / 2)
    ax.set_zlim(center[2] - max_range / 2, center[2] + max_range / 2)
    # ax.set_box_aspect([1,1,1])
    
    ax.legend(loc='upper left', bbox_to_anchor=(1,1))
    fig.tight_layout(rect=[0,0,0.85,1])

    caption = f"""
    图注:
    - 灰色表面: 原始3D网格模型。
    - 彩色路径: 通过蛇形连接算法连接后的填充路径段。
      不同颜色可能代表来自不同原始条带或连接组的路径（示意）。
    - 红色圆点 (示意): 突出显示可能的路径段间连接点。
    连接决策依据 (未直接在图上全部显示，为概念说明):
    1. 距离: 待连接的路径端点是否足够近？
    2. 法向相似性: 端点处的表面法向量是否足够相似？
    3. 碰撞检测: 连接两个端点的直线路径是否会与模型碰撞？
    只有当所有条件满足时，才会执行连接。
    """
    fig.text(0.02, 0.02, caption, transform=fig.transFigure, va="bottom", ha="left", fontsize=8, wrap=True)
    plt.show()


def plot_multi_step_tool_lift(slicer, paths_data, gcode_normal_hop, gcode_clearance_above_model_max):
    """
    图Z：可视化多轴G-code中的高级多步抬刀与精确接近运动逻辑。
    利用slicer内部的visualize_paths方法，该方法已包含抬刀路径的绘制。
    """
    print(f"调用内部 visualize_paths 进行 图Z 的绘制...")
    print(f"  Normal Hop for Viz: {gcode_normal_hop}, Clearance for Viz: {gcode_clearance_above_model_max}")

    # Call the slicer's own visualize_paths method
    # Ensure the slicer instance passed here is correctly initialized and has a mesh.
    if hasattr(slicer, 'visualize_paths'):
        slicer.visualize_paths(
            paths_data,
            show_normals=False, 
            normal_scale=0.5, 
            normal_hop_distance=gcode_normal_hop,
            clearance_above_model_max_viz=gcode_clearance_above_model_max
        )
        print("图Z 已由 slicer.visualize_paths 内部处理。自定义标题和图注将不适用。")
    else:
        print("错误: Slicer实例没有visualize_paths方法。将尝试绘制基本回退图。")
        # Fallback basic plot (this part would need its own fig and ax if used)
        fig = plt.figure(figsize=(12, 9)) # Fallback needs its own figure
        ax = fig.add_subplot(111, projection='3d')
        if slicer.mesh:
            ax.plot_trisurf(slicer.mesh.vertices[:,0], slicer.mesh.vertices[:,1], slicer.mesh.vertices[:,2], triangles=slicer.mesh.faces, alpha=0.1, color='gray')
        for p_data, _, _, _ in paths_data:
            if p_data is not None and len(p_data) > 0:
                ax.plot(p_data[:,0], p_data[:,1], p_data[:,2], 'b.-')
    
    # ax.set_title("图Z：多轴G-code中的高级多步抬刀与精确接近运动逻辑") # REMOVED/COMMENTED
    # # xlabel, ylabel, zlabel, aspect ratio, etc., should be handled by slicer.visualize_paths (REMOVED/COMMENTED)
    # if not hasattr(slicer, 'visualize_paths') or not slicer.visualize_paths.__module__ == "direct_projection_slicer":
    #     ax.set_xlabel("X (mm)")
    #     ax.set_ylabel("Y (mm)")
    #     ax.set_zlabel("Z (mm)")
        
    #     all_points_list = [slicer.mesh.vertices.copy()]
    #     if paths_data:
    #         for p_data_item, _, _, _ in paths_data: 
    #             if p_data_item is not None and len(p_data_item) > 0:
    #                 all_points_list.append(p_data_item)
        
    #     if len(all_points_list) > 1:
    #         all_points_combined = np.vstack(all_points_list)
    #         min_vals, max_vals = np.min(all_points_combined, axis=0), np.max(all_points_combined, axis=0)
    #     elif len(all_points_list) == 1:
    #         min_vals, max_vals = np.min(all_points_list[0], axis=0), np.max(all_points_list[0], axis=0)
    #     else:
    #         min_vals, max_vals = slicer.mesh.bounds

    #     center = (min_vals + max_vals) / 2.0
    #     ranges = max_vals - min_vals
    #     max_range = np.max(ranges)
    #     if max_range < 1e-6:
    #         max_range = 1.0

    #     ax.set_xlim(center[0] - max_range / 2, center[0] + max_range / 2)
    #     ax.set_ylim(center[1] - max_range / 2, center[1] + max_range / 2)
    #     ax.set_zlim(center[2] - max_range / 2, center[2] + max_range / 2)
    #     # ax.set_box_aspect([1,1,1])

    # # Legend should be handled by slicer.visualize_paths if it adds one. (REMOVED/COMMENTED)
    # # Otherwise, add a custom legend if needed.
    # if not hasattr(slicer, 'visualize_paths') or not ax.get_legend():
    #      ax.legend(loc='upper left', bbox_to_anchor=(1,1))

    # fig.tight_layout(rect=[0,0,0.85,1] if ax.get_legend() else [0,0,1,1]) # REMOVED/COMMENTED
    
    # caption = f"""  # REMOVED/COMMENTED
    # 图注:
    # - 灰色表面: 原始3D网格模型。
    # - 蓝色/绿色路径: 实际打印路径。
    # - 红色虚线/标记点: 代表G-code中的抬刀和移动路径。
    # 运动顺序示意:
    # (0) 路径段A打印结束 -> (1) 回抽 (不可见)
    # -> (2) 沿当前工具轴法向抬刀 (NormalHop: {gcode_normal_hop}mm)
    # -> (3) 工具姿态回正 (A=0, B=0)
    # -> (4) Z轴抬升至模型全局安全高度 (Clearance: {gcode_clearance_above_model_max}mm)
    # -> (5) 在安全高度移动至路径段B预接近点上方
    # -> (6) 工具姿态调整为路径段B起始姿态
    # -> (7) Z轴下降至路径段B的预接近点
    # -> (8) 反回抽 (不可见) -> (9) 精确接近并开始打印路径段B
    # """ 
    # fig.text(0.02, 0.02, caption, transform=fig.transFigure, va="bottom", ha="left", fontsize=8, wrap=True) # REMOVED/COMMENTED
    # plt.show() # slicer.visualize_paths will call plt.show()


if __name__ == "__main__":
    print("开始执行Matplotlib可视化脚本...")

    # --- Configuration ---
    # Use a relative path; ensure the STL is in the 'src' subdirectory from where script is run
    # Or provide an absolute path.
    
    # Determine script directory to robustly locate 'src'
    script_dir = os.path.dirname(os.path.abspath(__file__))
    default_mesh_filename = "src/m21_mnp_002.stl" # Example mesh
    mesh_path = os.path.join(script_dir, default_mesh_filename)

    if not os.path.exists(mesh_path):
        print(f"警告: 默认网格文件 '{mesh_path}' 未找到.")
        # Try a very common STL name if the specific one is missing, for robustness in testing
        fallback_mesh_path = os.path.join(script_dir, "src/sphere.stl") # Or any other simple STL
        if os.path.exists(fallback_mesh_path):
            print(f"将使用备用网格: '{fallback_mesh_path}'")
            mesh_path = fallback_mesh_path
        else:
            print(f"备用网格 '{fallback_mesh_path}' 也未找到。将使用占位符 Slicer。")
            # If no mesh, the placeholder slicer will use a trimesh.creation.box()
            # but we need to signal that the path is effectively None for the placeholder
            mesh_path_for_slicer = None # Signal to placeholder that no real mesh is loaded
            slicer_instance = DirectProjectionSlicer(mesh_path=mesh_path_for_slicer, slice_direction='y', target_surface_distance=0.4)

    if os.path.exists(mesh_path): # Check again in case fallback was found
         slicer_instance = DirectProjectionSlicer(
            mesh_path=mesh_path,
            target_surface_distance=4, # Example value, ADOS uses it for d_surf/2 trim
            slice_direction='y',         # 'x' or 'y'. For direct_offset, this is the step-over axis
            inward_normals=True,
            min_points_req=2
        )
    # If mesh_path is still None here, slicer_instance is already the placeholder from above

    # Parameters for create_projected_fill_paths (direct_offset strategy)
    target_bead_width_param = 4 # mm
    path_row_spacing_param = target_bead_width_param * 0.7  # Target 3D spacing
    path_max_segment_length_param = target_bead_width_param * 0.5 # Interpolation on strips
    offset_distance_param = target_bead_width_param / 2.0 # Offset from mesh bounds along offset_dir_axis
    proximity_threshold_param = 0.15 # mm, for hole avoidance in iterative slicer.
    
    adaptive_density_param = True
    iter_min_delta_y_factor_param = 0.1
    iter_max_delta_y_factor_param = 1.5
    iter_tolerance_abs_param = 0.05 #mm
    iter_max_iterations_per_step_param = 10
    iter_num_samples_for_spacing_calc_param = 7

    print(f"加载Slicer，网格: {mesh_path if mesh_path else 'Placeholder Box'}")
    print(f"使用 direct_offset 策略生成路径...")

    generated_paths, spacing_data = slicer_instance.create_projected_fill_paths(
        row_spacing=path_row_spacing_param,
        offset_distance=offset_distance_param,
        max_segment_length=path_max_segment_length_param,
        strategy='direct_offset',
        proximity_threshold=proximity_threshold_param,
        adaptive_density=adaptive_density_param,
        iter_min_delta_y_factor=iter_min_delta_y_factor_param,
        iter_max_delta_y_factor=iter_max_delta_y_factor_param,
        iter_tolerance_abs=iter_tolerance_abs_param,
        iter_max_iterations_per_step=iter_max_iterations_per_step_param,
        iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc_param
    )

    if not generated_paths:
        print("未能生成路径数据。无法进行可视化。")
    else:
        print(f"生成了 {len(generated_paths)} 个路径段。")
        if spacing_data:
            print(f"生成了 {len(spacing_data)} 条间距分析记录。")

        # --- G-code参数 (用于图Z的可视化参数匹配) ---
        gcode_normal_hop_param = 1.5         # mm
        gcode_clearance_above_model_max_param = 3.0   # mm

        # --- 调用可视化函数 ---
        print("开始生成 图X：ADOS策略核心原理...")
        plot_ados_principle(slicer_instance, generated_paths, spacing_data, 
                            slicer_instance.axis_index, # Pass the offset_dir_axis from slicer
                            path_row_spacing_param)     # Pass target 3D spacing

        print("开始生成 图Y：智能蛇形连接算法...")
        plot_snake_connection_logic(slicer_instance, generated_paths)
        
        print("开始生成 图Z：多步抬刀与精确接近运动逻辑...")
        plot_multi_step_tool_lift(slicer_instance, generated_paths, 
                                  gcode_normal_hop_param, gcode_clearance_above_model_max_param)

    print("Matplotlib可视化脚本执行完毕。") 